/**
 * ⚠️  CRITICAL RULE: TEAMS AND KARTS CREATION POLICY ⚠️
 *
 * Teams, Karts, and Competitors MUST ONLY be created from init messages with grid data!
 *
 * ALLOWED:
 * - Creating teams/karts/competitors during init message processing with grid data
 * - Updating existing teams/karts/competitors from regular messages
 *
 * FORBIDDEN:
 * - Creating teams/karts/competitors from regular update messages
 * - Creating teams/karts/competitors from any source other than init+grid
 * - Auto-creating missing entities during updates
 *
 * If entities don't exist during updates, the update is skipped with a warning.
 * This ensures data integrity and prevents orphaned or inconsistent entities.
 */

import { EventEmitter } from 'events';
import mongoose from 'mongoose';
import { ApexSession, ApexKart, ApexTeam, ApexLap, ApexCompetitor, ApexPitStop } from '../models/ApexModels';
import { parseGridData, GridParseResult } from './apexGridParser';
import { parseLogMessage, ParsedMessage } from './apexMessageParser';
import apexCache from './apexCache';

export interface ApexParserConfig {
  sessionId?: string | mongoose.Types.ObjectId;
  raceId?: string;
  enableLogging?: boolean;
}
export interface GridData {
  drivers: Record<string, any>;
  header_labels: Record<string, string>;
  header_types: Record<string, string>;
  id: number;
}

export class ApexParser extends EventEmitter {
  private config: ApexParserConfig;
  private currentSession: any = null;
  private gridData: GridData | null = null;
  private sessionFields = new Set([
    "best", "css", "comments", "effects", "init", "title1", "title2", 
    "dyn1", "light", "wth1", "wth2", "wth3", "track", "com", "grid", "msg"
  ]);

  constructor(config: ApexParserConfig = {}) {
    super();
    this.config = {
      enableLogging: true,
      ...config
    };

    // Initialize with existing session if sessionId is provided
    if (this.config.sessionId) {
      this.initializeWithExistingSession();
    }
  }

  /**
   * Initialize parser with existing session
   */
  async initializeWithExistingSession(): Promise<void> {
    if (!this.config.sessionId) return;

    try {
      let session: any;

      if (typeof this.config.sessionId === 'string') {
        try {
          const objectId = new mongoose.Types.ObjectId(this.config.sessionId);
          session = await ApexSession.findById(objectId);
        } catch (error) {
          if (this.config.enableLogging) {
            console.warn(`Invalid session ID format: ${this.config.sessionId}`);
          }
          return;
        }
      } else {
        session = await ApexSession.findById(this.config.sessionId);
      }

      if (session) {
        this.currentSession = session;

        // Load grid data if available
        if (session.gridData) {
          this.gridData = session.gridData;
          if (this.config.enableLogging) {
            console.log(`✅ Loaded existing session: ${session.title1} - ${session.title2}`);
            console.log(`   Session ID: ${session._id}`);
            console.log(`   Grid data: ${this.gridData ? 'Available' : 'Not available'}`);
          }
        }
      } else {
        if (this.config.enableLogging) {
          console.warn(`Session not found: ${this.config.sessionId}`);
        }
      }
    } catch (error) {
      if (this.config.enableLogging) {
        console.error('Error initializing with existing session:', error);
      }
    }
  }

  /**
   * Parse a complete log file and process all messages
   */
  async parseLogFile(logContent: string): Promise<void> {
    const lines = logContent.split('\n');
    
    for (const line of lines) {
      if (line.trim()) {
        try {
          await this.parseLogLine(line);
        } catch (error) {
          if (this.config.enableLogging) {
            console.error('Error parsing line:', line, error);
          }
        }
      }
    }
  }

  /**
   * Parse a single log line or multi-line message
   */
  async parseLogLine(content: string): Promise<void> {
    try {
      // First, try to parse the entire content as a single multi-line message
      const multiLineMessage = parseLogMessage(content);

      if (multiLineMessage && Object.keys(multiLineMessage.data).length > 1) {
        // This looks like a complete multi-line message (e.g., init message with multiple fields)
        if (this.config.enableLogging) {
          console.log(`Processing multi-line message with ${Object.keys(multiLineMessage.data).length} fields`);
        }
        await this.processMessage(multiLineMessage);
        return;
      }

      // If that didn't work, fall back to processing each line separately
      const lines = content.split('\n').filter(line => line.trim().length > 0);

      for (const line of lines) {
        const parsedMessage = parseLogMessage(line);
        if (parsedMessage) {
          await this.processMessage(parsedMessage);
        } else if (this.config.enableLogging) {
          console.warn('Failed to parse line:', line.substring(0, 100));
        }
      }
    } catch (error) {
      if (this.config.enableLogging) {
        console.error('Error parsing log content:', error);
      }
      throw error;
    }
  }

  /**
   * Process a parsed message
   * Simplified logic: if grid found -> create database elements, else -> update existing elements
   */
  private async processMessage(message: ParsedMessage): Promise<void> {
    if (this.config.enableLogging) {
      console.log(`Processing message type: ${message.type}, fields: ${Object.keys(message.data).length}`);
    }

    // Simple logic: check if grid is present
    if (message.data.grid) {
      // Grid found -> parse and create database elements
      if (this.config.enableLogging) {
        console.log('🏁 Grid found - creating database elements');
      }
      await this.handleGridMessage(message.data);
    } else {
      // No grid -> update existing database elements
      if (this.config.enableLogging) {
        console.log(`🔄 No grid - updating existing elements (${Object.keys(message.data).length} fields)`);
      }
      await this.handleUpdateMessage(message.data);
    }
  }

  /**
   * Handle message with grid data - create database elements
   */
  private async handleGridMessage(messageData: Record<string, any>): Promise<void> {
    try {
      // Parse grid data
      if (messageData.grid) {
        if (this.config.enableLogging) {
          console.log('📊 Parsing grid data...');
        }
        this.gridData = parseGridData(messageData.grid.value);
        if (this.config.enableLogging) {
          console.log(`   Parsed ${Object.keys(this.gridData.drivers).length} drivers from grid`);
        }
      }

      // Create or update session
      await this.createOrUpdateSession(messageData);

      // Create teams and karts from grid data
      if (this.gridData) {
        await this.createTeamsAndKartsFromGrid();
      }

    } catch (error) {
      console.error('Error handling grid message:', error);
    }
  }

  /**
   * Handle message without grid data - update existing database elements
   */
  private async handleUpdateMessage(messageData: Record<string, any>): Promise<void> {
    try {
      // Ensure we have a session
      if (!this.currentSession) {
        if (this.config.enableLogging) {
          console.log('No current session, creating default session');
        }
        await this.createDefaultSession();
      }

      // Process all fields as updates
      await this.processAllFields(messageData);

    } catch (error) {
      console.error('Error handling update message:', error);
    }
  }

  /**
   * Process all fields in a message (simplified)
   */
  private async processAllFields(fields: Record<string, any>): Promise<void> {
    for (const [key, data] of Object.entries(fields)) {
      try {
        // Convert data to expected format if needed
        const fieldData = typeof data === 'object' && data.value !== undefined
          ? data
          : { type: key, value: String(data) };

        if (this.sessionFields.has(key)) {
          // Session field
          await this.handleSessionField(key, fieldData);
        } else if (key.startsWith('r') && key.includes('c')) {
          // Driver/competitor field (format: r{id}c{column})
          await this.handleDriverMessage(key, fieldData);
        } else if (this.config.enableLogging) {
          console.log(`Unknown field: ${key}`);
        }
      } catch (error) {
        console.error(`Error processing field ${key}:`, error);
      }
    }
  }



  /**
   * Create or update a race session
   */
  private async createOrUpdateSession(sessionData: Record<string, any>): Promise<void> {
    try {
      // Parse grid data if present (only once)
      if (sessionData.grid && !this.gridData) {
        if (this.config.enableLogging) {
          console.log('📊 Grid data found, parsing...');
          console.log(`   Grid data length: ${sessionData.grid.value.length} characters`);
        }
        this.gridData = parseGridData(sessionData.grid.value);
        if (this.config.enableLogging) {
          console.log(`   Parsed ${Object.keys(this.gridData.drivers).length} drivers from grid`);
        }
      } else if (!sessionData.grid) {
        if (this.config.enableLogging) {
          console.log('ℹ️ No grid data in session data');
        }
      }

      // Create or find session using ObjectId
      let session: any;

      if (this.config.sessionId) {
        // If sessionId is provided, try to find existing session by _id
        if (typeof this.config.sessionId === 'string') {
          // Convert string to ObjectId if needed
          try {
            const objectId = new mongoose.Types.ObjectId(this.config.sessionId);
            session = await ApexSession.findById(objectId);
          } catch (error) {
            // If string is not a valid ObjectId, create new session
            session = null;
          }
        } else {
          // Already an ObjectId
          session = await ApexSession.findById(this.config.sessionId);
        }
      }

      if (!session) {
        // Create new session with specific _id if provided
        const sessionData_obj: any = {
          raceId: this.config.raceId || sessionData.title1?.value || 'unknown',
          title1: sessionData.title1?.value || '',
          title2: sessionData.title2?.value || '',
          track: sessionData.track?.value || '',
          startTime: new Date(),
          isActive: true,
          gridData: this.gridData
        };

        // Set _id if sessionId is provided
        if (this.config.sessionId) {
          if (typeof this.config.sessionId === 'string') {
            try {
              sessionData_obj._id = new mongoose.Types.ObjectId(this.config.sessionId);
            } catch (error) {
              // If invalid ObjectId string, let MongoDB auto-generate
            }
          } else {
            sessionData_obj._id = this.config.sessionId;
          }
        }

        session = new ApexSession(sessionData_obj);
        await session.save();

        if (this.config.enableLogging) {
          console.log('✅ Session created:', session._id);
          console.log(`   Title: ${session.title1} - ${session.title2}`);
          console.log(`   Track: ${session.track}`);
        }

        // Emit session created event
        this.emit('sessionCreated', session);
      }

      this.currentSession = session;

      // Create teams and karts from grid data if available
      if (this.gridData) {
        await this.createTeamsAndKartsFromGrid();
      } else {
        if (this.config.enableLogging) {
          console.log('ℹ️ No grid data available for team/kart creation');
        }
      }

    } catch (error) {
      console.error('Error creating/updating session:', error);
    }
  }

  /**
   * Create a default session when none exists
   */
  private async createDefaultSession(): Promise<void> {
    let session: any;

    if (this.config.sessionId) {
      // Try to find existing session by _id
      if (typeof this.config.sessionId === 'string') {
        try {
          const objectId = new mongoose.Types.ObjectId(this.config.sessionId);
          session = await ApexSession.findById(objectId);
        } catch (error) {
          session = null;
        }
      } else {
        session = await ApexSession.findById(this.config.sessionId);
      }
    }

    if (!session) {
      const sessionData_obj: any = {
        raceId: this.config.raceId || 'default_race',
        title1: 'Default Race',
        title2: 'Session',
        track: 'Unknown Track',
        startTime: new Date(),
        isActive: true,
        gridData: null
      };

      // Set _id if sessionId is provided
      if (this.config.sessionId) {
        if (typeof this.config.sessionId === 'string') {
          try {
            sessionData_obj._id = new mongoose.Types.ObjectId(this.config.sessionId);
          } catch (error) {
            // If invalid ObjectId string, let MongoDB auto-generate
          }
        } else {
          sessionData_obj._id = this.config.sessionId;
        }
      }

      session = new ApexSession(sessionData_obj);
      await session.save();

      if (this.config.enableLogging) {
        console.log('Created default session:', session._id);
      }
    }

    this.currentSession = session;
  }

  // REMOVED: createCompetitorsFromDriverData and createCompetitorEntities methods
  // Teams/competitors should ONLY be created during init message grid parsing
  // All subsequent messages are updates only

  /**
   * Create teams and karts from grid data (OPTIMIZED with bulk operations)
   *
   * ⚠️  CRITICAL: Teams and karts MUST ONLY be created from init messages with grid data!
   * ⚠️  NEVER create teams/karts from regular update messages or any other source!
   * ⚠️  This is the ONLY place where teams and karts should be created in the entire system!
   */
  private async createTeamsAndKartsFromGrid(): Promise<void> {
    if (!this.gridData || !this.currentSession) {
      if (this.config.enableLogging) {
        console.log(`❌ Cannot create teams: gridData=${!!this.gridData}, currentSession=${!!this.currentSession}`);
      }
      return;
    }

    const driverCount = Object.keys(this.gridData.drivers).length;
    if (this.config.enableLogging) {
      console.log(`🚀 Creating ${driverCount} teams from grid data using BULK operations`);
    }

    const startTime = Date.now();

    // Prepare bulk data arrays
    const teamsToCreate: any[] = [];
    const kartsToCreate: any[] = [];
    const competitorsToCreate: any[] = [];

    // Process all drivers and prepare bulk data
    for (const [driverId, driverData] of Object.entries(this.gridData.drivers)) {
      try {
        // Extract team/driver information
        const teamName = driverData.dr?.value || `Team ${driverId}`;
        const kartNumber = parseInt(driverData.no?.value || '0') || 0;
        const competitorId = driverId.replace('r', '');

        // Prepare team data
        teamsToCreate.push({
          sessionId: this.currentSession._id,
          teamId: competitorId,
          name: teamName,
          number: kartNumber, // Add number field for frontend compatibility
          kartNumber,
          currentKartId: null, // Will be set after kart creation
          pastKarts: [], // Initialize as empty array
          pits: [], // Initialize as empty array
          drivers: [teamName],
          nationality: driverData.nat?.value || 'Unknown',
          status: 'active',
          position: null,
          totalLaps: 0,
          lastLapTime: null,
          bestLapTime: null,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        });

        // Prepare kart data
        kartsToCreate.push({
          sessionId: this.currentSession._id,
          number: kartNumber, // Add number field for frontend compatibility
          kartNumber,
          speed: 4, // Default speed level (1-5 scale, 3 = medium)
          teamId: competitorId,
          currentTeamId: null, // Will be set to ObjectId after team creation
          currentRowId: null, // Initialize as null
          currentDriverId: competitorId,
          status: 'on_track', // Use valid enum value instead of 'racing'
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        });

        // Prepare competitor data
        competitorsToCreate.push({
          sessionId: this.currentSession._id,
          competitorId,
          teamId: competitorId,
          name: teamName,
          kartNumber,
          drivers: [teamName],
          createdAt: new Date(),
          updatedAt: new Date()
        });

      } catch (error) {
        console.error(`Error preparing data for driver ${driverId}:`, error);
      }
    }

    try {
      // Execute bulk operations sequentially to handle relationships
      // First create teams
      const teamResults = await ApexTeam.insertMany(teamsToCreate, { ordered: false });

      // Create a map of teamId to team ObjectId for kart updates
      const teamIdToObjectId = new Map<string, mongoose.Types.ObjectId>();
      for (const team of teamResults) {
        teamIdToObjectId.set(team.teamId, team._id as mongoose.Types.ObjectId);
      }

      // Update kart data with proper team ObjectId references
      for (const kartData of kartsToCreate) {
        const teamObjectId = teamIdToObjectId.get(kartData.teamId);
        if (teamObjectId) {
          kartData.currentTeamId = teamObjectId;
        }
      }

      // Create karts with proper team references
      const kartResults = await ApexKart.insertMany(kartsToCreate, { ordered: false });

      // Create a map of teamId to kart ObjectId for team updates
      const teamIdToKartId = new Map<string, mongoose.Types.ObjectId>();
      for (const kart of kartResults) {
        teamIdToKartId.set(kart.teamId, kart._id as mongoose.Types.ObjectId);
      }

      // Update teams with kart references
      const teamUpdates = teamResults.map(team => ({
        updateOne: {
          filter: { _id: team._id },
          update: { $set: { currentKartId: teamIdToKartId.get(team.teamId) } }
        }
      }));

      if (teamUpdates.length > 0) {
        await ApexTeam.bulkWrite(teamUpdates, { ordered: false });
      }

      // Create competitors
      const competitorResults = await ApexCompetitor.insertMany(competitorsToCreate, { ordered: false });

      const endTime = Date.now();
      const duration = endTime - startTime;

      if (this.config.enableLogging) {
        console.log(`✅ BULK CREATION COMPLETED in ${duration}ms:`);
        console.log(`   👥 Teams created: ${teamResults.length}`);
        console.log(`   🏎️ Karts created: ${kartResults.length}`);
        console.log(`   🏁 Competitors created: ${competitorResults.length}`);
        console.log(`   ⚡ Speed: ${Math.round(driverCount / (duration / 1000))} entities/second`);
      }

      // Emit team created events for all teams
      for (const team of teamResults) {
        this.emit('teamCreated', team);
      }

      // Create apex karts for pit rows after successful team/kart creation
      try {
        const { ApexPitRowService } = await import('./apexPitRowService');
        await ApexPitRowService.createApexKartsForPitRows(this.currentSession._id.toString());
        if (this.config.enableLogging) {
          console.log('✅ Created apex karts for pit rows after grid parsing');
        }
      } catch (pitRowError) {
        console.error('Error creating apex karts for pit rows:', pitRowError);
        // Don't fail the entire operation for pit row issues
      }

    } catch (error) {
      console.error('Error in bulk team creation:', error);

      // Fallback to individual creation if bulk fails
      if (this.config.enableLogging) {
        console.log('🔄 Falling back to individual creation...');
      }
      await this.createTeamsAndKartsFromGridIndividual();
    }
  }

  /**
   * Fallback method: Create teams individually (slower but more reliable)
   *
   * ⚠️  CRITICAL: This is also ONLY for init messages with grid data!
   * ⚠️  NEVER call this from regular update messages!
   */
  private async createTeamsAndKartsFromGridIndividual(): Promise<void> {
    if (!this.gridData || !this.currentSession) return;

    if (this.config.enableLogging) {
      console.log(`🔄 Creating teams individually as fallback...`);
    }

    for (const [driverId, driverData] of Object.entries(this.gridData.drivers)) {
      try {
        const teamName = driverData.dr?.value || `Team ${driverId}`;
        const kartNumber = parseInt(driverData.no?.value || '0') || 0;
        const competitorId = driverId.replace('r', '');

        // Create team
        const team = new ApexTeam({
          sessionId: this.currentSession._id,
          teamId: competitorId,
          name: teamName,
          number: kartNumber, // Set team number to match kart number for frontend compatibility
          kartNumber,
          currentKartId: null, // Will be set after kart creation
          pastKarts: [], // Initialize as empty array
          pits: [], // Initialize as empty array
          drivers: [teamName],
          nationality: driverData.nat?.value || 'Unknown',
          status: 'active',
          isActive: true
        });
        await team.save();

        // Create kart
        const kart = new ApexKart({
          sessionId: this.currentSession._id,
          number: kartNumber, // Set kart number for frontend compatibility
          kartNumber,
          speed: 3, // Default speed level (1-5 scale, 3 = medium)
          teamId: competitorId,
          currentTeamId: team._id, // Set to the actual team ObjectId
          currentRowId: null, // Initialize as null
          currentDriverId: competitorId,
          status: 'on_track', // Use valid enum value instead of 'racing'
          isActive: true
        });
        await kart.save();

        // Update team with kart reference
        team.currentKartId = kart._id as mongoose.Types.ObjectId;
        await team.save();

        // Create competitor
        const competitor = new ApexCompetitor({
          sessionId: this.currentSession._id,
          competitorId,
          teamId: competitorId,
          name: teamName,
          kartNumber
        });
        await competitor.save();

        // Emit team created event
        this.emit('teamCreated', team);

      } catch (error) {
        console.error(`Error creating entities for driver ${driverId}:`, error);
      }
    }

    // Create apex karts for pit rows after successful individual creation
    try {
      const { ApexPitRowService } = await import('./apexPitRowService');
      await ApexPitRowService.createApexKartsForPitRows(this.currentSession._id.toString());
      if (this.config.enableLogging) {
        console.log('✅ Created apex karts for pit rows after individual grid parsing');
      }
    } catch (pitRowError) {
      console.error('Error creating apex karts for pit rows:', pitRowError);
      // Don't fail the entire operation for pit row issues
    }
  }

  /**
   * Process driver/competitor updates from a message
   *
   * ⚠️  CRITICAL: This method NEVER creates teams/karts/competitors!
   * ⚠️  It only processes updates for EXISTING entities created during init!
   */
  private async processDriverUpdates(fields: Record<string, { type: string; value: string }>): Promise<void> {
    if (!this.currentSession) {
      if (this.config.enableLogging) {
        console.warn('No current session for driver updates, creating default session');
      }
      // Create a default session if none exists
      await this.createDefaultSession();
    }

    // ⚠️  CRITICAL: DO NOT create competitors/teams/karts here!
    // ⚠️  They should ONLY be created during init message grid parsing!
    // ⚠️  All messages after init are update messages only!

    // Process the individual field updates
    for (const [key, { type, value }] of Object.entries(fields)) {
      if (key.startsWith("r")) {
        await this.handleDriverMessage(key, { type, value });
      }
    }
  }

  /**
   * Process message updates (non-init messages) with batch optimization
   *
   * ⚠️  CRITICAL: This method NEVER creates teams/karts/competitors!
   * ⚠️  It only processes updates for EXISTING entities created during init!
   * ⚠️  If entities don't exist, updates are skipped with warnings!
   */
  private async processMessageUpdates(fields: Record<string, { type: string; value: string }>): Promise<void> {
    if (!this.currentSession) {
      if (this.config.enableLogging) {
        console.warn('❌ No current session for message updates');
      }
      return;
    }

    // Separate fields by type for batch processing
    const sessionFields: Array<[string, { type: string; value: string }]> = [];
    const driverFields: Array<[string, { type: string; value: string }]> = [];
    const unknownFields: Array<[string, { type: string; value: string }]> = [];

    // Categorize fields
    for (const [key, data] of Object.entries(fields)) {
      if (this.sessionFields.has(key)) {
        sessionFields.push([key, data]);
      } else if (key.startsWith("r")) {
        driverFields.push([key, data]);
      } else {
        unknownFields.push([key, data]);
      }
    }

    if (this.config.enableLogging) {
      console.log(`🔄 Processing ${Object.keys(fields).length} fields: ${sessionFields.length} session, ${driverFields.length} driver, ${unknownFields.length} unknown`);
    }

    // Process in batches for better performance
    await Promise.all([
      this.processBatchSessionFields(sessionFields),
      this.processBatchDriverFields(driverFields)
    ]);

    // Log unknown fields
    if (unknownFields.length > 0 && this.config.enableLogging) {
      console.warn(`❓ Unknown fields: ${unknownFields.map(([key]) => key).join(', ')}`);
    }
  }

  /**
   * Process session fields in batch
   */
  private async processBatchSessionFields(sessionFields: Array<[string, { type: string; value: string }]>): Promise<void> {
    if (sessionFields.length === 0) return;

    // For now, process session fields individually as they're typically few
    for (const [key, data] of sessionFields) {
      await this.handleSessionField(key, data);
    }
  }

  /**
   * Process driver fields in batch for maximum performance
   */
  private async processBatchDriverFields(driverFields: Array<[string, { type: string; value: string }]>): Promise<void> {
    if (driverFields.length === 0) return;

    // Group driver updates by type for batch processing
    const lapTimeUpdates: Array<{ competitorId: string; columnId: string; value: string; type: string }> = [];
    const positionUpdates: Array<{ competitorId: string; position: number }> = [];
    const statusUpdates: Array<{ competitorId: string; status: string }> = [];
    const pitUpdates: Array<{ competitorId: string; columnId: string; value: string; type: string }> = [];
    const otherUpdates: Array<[string, { type: string; value: string }]> = [];

    // Categorize driver updates
    for (const [key, { type, value }] of driverFields) {
      const parts = key.split("c");
      if (parts.length >= 2 && parts[0] && parts[1]) {
        const competitorId = parts[0].replace('r', '');
        const columnId = parts[1];

        // Get grid column type
        let gridColumnType: string | null = null;
        if (this.gridData && this.gridData.header_types) {
          const columnKey = `c${columnId}`;
          gridColumnType = this.gridData.header_types[columnKey] || this.gridData.header_types[columnId] || null;
        }

        if (gridColumnType === 'llp' || gridColumnType === 'blp') {
          // Lap time update
          if (value && value.includes(':')) {
            lapTimeUpdates.push({ competitorId, columnId, value, type: gridColumnType });
          }
        } else if (gridColumnType === 'rk') {
          // Position update
          const position = parseInt(value);
          if (!isNaN(position)) {
            positionUpdates.push({ competitorId, position });
          }
        } else if (gridColumnType === 'sta') {
          // Status update
          statusUpdates.push({ competitorId, status: value });
        } else if (gridColumnType === 'pit' || gridColumnType === 'otr') {
          // Pit update
          pitUpdates.push({ competitorId, columnId, value, type: gridColumnType });
        } else {
          // Other updates - process individually
          otherUpdates.push([key, { type, value }]);
        }
      } else {
        // Simple competitor field - process individually
        otherUpdates.push([key, { type, value }]);
      }
    }

    // Process batches in parallel
    await Promise.all([
      this.processBatchLapTimes(lapTimeUpdates),
      this.processBatchPositions(positionUpdates),
      this.processBatchStatuses(statusUpdates),
      this.processBatchPitUpdates(pitUpdates),
      this.processBatchOtherUpdates(otherUpdates)
    ]);
  }

  /**
   * Process lap time updates in batch (FASTEST - bulk database operations)
   */
  private async processBatchLapTimes(lapTimeUpdates: Array<{ competitorId: string; columnId: string; value: string; type: string }>): Promise<void> {
    if (lapTimeUpdates.length === 0) return;

    const startTime = Date.now();
    const lapsToInsert: any[] = [];
    const teamUpdates: any[] = [];
    const kartUpdates: any[] = [];

    // Prepare bulk data
    for (const { competitorId, value } of lapTimeUpdates) {
      try {
        // Parse lap time
        const lapTimeSeconds = this.parseTimeToSeconds(value);
        if (lapTimeSeconds <= 0) continue;

        // ⚠️  CRITICAL: Only process lap times for existing competitors!
        // ⚠️  Competitors must be created during init with grid data first!
        let competitor = await apexCache.getCompetitor(this.currentSession?._id?.toString() || '', competitorId);
        if (!competitor) {
          // Try to get from database
          competitor = await ApexCompetitor.findOne({
            sessionId: this.currentSession?._id,
            competitorId
          });

          if (!competitor) {
            if (this.config.enableLogging) {
              console.warn(`❌ Competitor ${competitorId} not found for lap time - teams must be created from init message with grid data first`);
            }
            continue;
          }
        }

        // Get current lap count from cache (much faster than database)
        const currentLapCount = await apexCache.getLapCount(this.currentSession?._id?.toString() || '', competitorId);

        // Prepare lap data
        lapsToInsert.push({
          sessionId: this.currentSession?._id,
          competitorId,
          teamId: competitor.teamId,
          kartNumber: competitor.kartNumber,
          lapNumber: currentLapCount + 1,
          lapTime: lapTimeSeconds,
          lapTimeFormatted: value,
          timestamp: new Date(),
          isPersonalBest: false, // Will be calculated after insert
          isBestLap: false,      // Will be calculated after insert
          createdAt: new Date()
        });

        // Prepare team update
        teamUpdates.push({
          updateOne: {
            filter: { sessionId: this.currentSession?._id, teamId: competitor.teamId },
            update: {
              $inc: { totalLaps: 1 },
              $set: {
                lastLapTime: lapTimeSeconds,
                lastLapTimeFormatted: value,
                updatedAt: new Date()
              }
            }
          }
        });

        // Also prepare kart update with lap time information
        kartUpdates.push({
          updateOne: {
            filter: { sessionId: this.currentSession?._id, kartNumber: competitor.kartNumber },
            update: {
              $inc: { totalLaps: 1 },
              $set: {
                lastLapTimeFormatted: value,
                updatedAt: new Date()
              }
            }
          }
        });

      } catch (error) {
        console.error(`Error preparing lap time for competitor ${competitorId}:`, error);
      }
    }

    if (lapsToInsert.length === 0) return;

    try {
      // Bulk insert laps
      const insertedLaps = await ApexLap.insertMany(lapsToInsert, { ordered: false });

      // Bulk update teams
      if (teamUpdates.length > 0) {
        await ApexTeam.bulkWrite(teamUpdates, { ordered: false });
      }

      // Bulk update karts
      if (kartUpdates.length > 0) {
        await ApexKart.bulkWrite(kartUpdates, { ordered: false });
      }

      // Update best lap flags in batch
      await this.updateBestLapFlags(insertedLaps);

      const duration = Date.now() - startTime;
      if (this.config.enableLogging) {
        console.log(`⚡ Batch processed ${lapsToInsert.length} lap times in ${duration}ms (${Math.round(lapsToInsert.length / (duration / 1000))} laps/sec)`);
      }

      // Update cache and emit events for all laps
      for (const lap of insertedLaps) {
        // Update cache for faster subsequent queries
        apexCache.incrementLapCount(this.currentSession?._id?.toString() || '', lap.competitorId);
        apexCache.updateBestLap(this.currentSession?._id?.toString() || '', lap.competitorId, lap.lapTime);

        this.emit('lapRecorded', {
          competitorId: lap.competitorId,
          teamId: lap.teamId,
          kartNumber: lap.kartNumber,
          lapNumber: lap.lapNumber,
          lapTime: lap.lapTime,
          lapTimeFormatted: lap.lapTimeFormatted,
          isPersonalBest: lap.isPersonalBest,
          isBestLap: lap.isBestLap
        });
      }

    } catch (error) {
      console.error('Error in batch lap time processing:', error);
      // Fallback to individual processing
      for (const update of lapTimeUpdates) {
        try {
          await this.handleLapTimeUpdate(update.competitorId, update.value, update.type);
        } catch (err) {
          console.error(`Error processing individual lap time for ${update.competitorId}:`, err);
        }
      }
    }
  }

  /**
   * Process position updates in batch
   */
  private async processBatchPositions(positionUpdates: Array<{ competitorId: string; position: number }>): Promise<void> {
    if (positionUpdates.length === 0) return;

    const bulkOps = positionUpdates.map(({ competitorId, position }) => ({
      updateOne: {
        filter: { sessionId: this.currentSession?._id, teamId: competitorId },
        update: { $set: { position, updatedAt: new Date() } }
      }
    }));

    try {
      await ApexTeam.bulkWrite(bulkOps, { ordered: false });
      if (this.config.enableLogging) {
        console.log(`⚡ Batch updated ${positionUpdates.length} positions`);
      }
    } catch (error) {
      console.error('Error in batch position updates:', error);
    }
  }

  /**
   * Process status updates in batch
   */
  private async processBatchStatuses(statusUpdates: Array<{ competitorId: string; status: string }>): Promise<void> {
    if (statusUpdates.length === 0) return;

    const bulkOps = statusUpdates.map(({ competitorId, status }) => ({
      updateOne: {
        filter: { sessionId: this.currentSession?._id, teamId: competitorId },
        update: { $set: { status, updatedAt: new Date() } }
      }
    }));

    try {
      await ApexTeam.bulkWrite(bulkOps, { ordered: false });
      if (this.config.enableLogging) {
        console.log(`⚡ Batch updated ${statusUpdates.length} statuses`);
      }
    } catch (error) {
      console.error('Error in batch status updates:', error);
    }
  }

  /**
   * Process pit updates in batch
   */
  private async processBatchPitUpdates(pitUpdates: Array<{ competitorId: string; columnId: string; value: string; type: string }>): Promise<void> {
    if (pitUpdates.length === 0) return;

    // For now, process pit updates individually as they require complex logic
    for (const update of pitUpdates) {
      try {
        await this.handlePitUpdate(update.competitorId, update.value, update.type);
      } catch (error) {
        console.error(`Error processing pit update for ${update.competitorId}:`, error);
      }
    }
  }

  /**
   * Process other updates individually
   */
  private async processBatchOtherUpdates(otherUpdates: Array<[string, { type: string; value: string }]>): Promise<void> {
    if (otherUpdates.length === 0) return;

    for (const [key, data] of otherUpdates) {
      try {
        await this.handleDriverMessage(key, data);
      } catch (error) {
        console.error(`Error processing other update ${key}:`, error);
      }
    }
  }

  /**
   * Update best lap flags in batch
   */
  private async updateBestLapFlags(insertedLaps: any[]): Promise<void> {
    if (insertedLaps.length === 0) return;

    try {
      // Group laps by competitor for personal best calculation
      const lapsByCompetitor = new Map<string, any[]>();
      for (const lap of insertedLaps) {
        if (!lapsByCompetitor.has(lap.competitorId)) {
          lapsByCompetitor.set(lap.competitorId, []);
        }
        lapsByCompetitor.get(lap.competitorId)!.push(lap);
      }

      const bulkOps: any[] = [];

      // Calculate personal bests
      for (const [competitorId, laps] of lapsByCompetitor) {
        // Get all laps for this competitor
        const allLaps = await ApexLap.find({
          sessionId: this.currentSession?._id,
          competitorId
        }).sort({ lapTime: 1 }).lean();

        if (allLaps.length > 0 && allLaps[0]) {
          const bestLapTime = allLaps[0].lapTime;

          // Mark personal bests
          for (const lap of laps) {
            if (lap.lapTime === bestLapTime) {
              bulkOps.push({
                updateOne: {
                  filter: { _id: lap._id },
                  update: { $set: { isPersonalBest: true } }
                }
              });
            }
          }
        }
      }

      // Calculate session best lap
      const sessionBestLap = await ApexLap.findOne({
        sessionId: this.currentSession?._id
      }).sort({ lapTime: 1 }).lean();

      if (sessionBestLap) {
        // Mark session best laps
        for (const lap of insertedLaps) {
          if (lap.lapTime === sessionBestLap.lapTime) {
            bulkOps.push({
              updateOne: {
                filter: { _id: lap._id },
                update: { $set: { isBestLap: true } }
              }
            });
          }
        }
      }

      // Execute bulk updates
      if (bulkOps.length > 0) {
        await ApexLap.bulkWrite(bulkOps, { ordered: false });
      }

    } catch (error) {
      console.error('Error updating best lap flags:', error);
    }
  }

  /**
   * Handle session field updates
   */
  private async handleSessionField(key: string, { type, value }: { type: string; value: string }): Promise<void> {
    if (!this.currentSession) return;

    try {
      // Update session with new field data
      await ApexSession.findByIdAndUpdate(this.currentSession._id, {
        [`sessionData.${key}`]: { type, value },
        lastUpdated: new Date()
      });

      if (this.config.enableLogging) {
        console.log(`Session field updated: ${key} = ${type} ${value}`);
      }
    } catch (error) {
      console.error('Error updating session field:', error);
    }
  }

  /**
   * Handle driver/competitor message updates
   */
  private async handleDriverMessage(key: string, { type, value }: { type: string; value: string }): Promise<void> {
    if (this.config.enableLogging) {
      console.log(`🔍 handleDriverMessage called with key="${key}", type="${type}", value="${value}"`);
    }

    // Handle simple competitor fields like r45396|#|1
    if (key.match(/^r\d+$/)) {
      const competitorId = key.replace('r', '');

      // Only process updates for existing competitors
      const existingCompetitor = await ApexCompetitor.findOne({
        sessionId: this.currentSession?._id,
        competitorId
      });

      if (!existingCompetitor) {
        if (this.config.enableLogging) {
          console.log(`❌ Competitor ${competitorId} not found - skipping update (${type} = ${value})`);
        }
        return;
      }

      if (this.config.enableLogging) {
        console.log(`Competitor ${competitorId}: ${type} = ${value}`);
      }
      return;
    }

    // Handle competitor column fields like r45396c9
    const parts = key.split("c");
    if (parts.length < 2) {
      if (this.config.enableLogging) {
        console.log(`⚠️ Invalid key format: ${key} (expected format: r12345c9)`);
      }
      return;
    }

    const [driverId, columnId] = parts;
    if (!driverId || !columnId) {
      if (this.config.enableLogging) {
        console.log(`⚠️ Invalid key parts: driverId="${driverId}", columnId="${columnId}"`);
      }
      return;
    }

    const competitorId = driverId.replace('r', '');

    if (this.config.enableLogging) {
      console.log(`🎯 Processing column update: competitor=${competitorId}, column=c${columnId}, type=${type}, value=${value}`);
    }

    if (!this.currentSession) {
      if (this.config.enableLogging) {
        console.log(`❌ No current session available`);
      }
      return;
    }

    // ⚠️  CRITICAL: Only process updates for existing competitors!
    // ⚠️  NEVER create competitors here - they must be created during init with grid data!
    const existingCompetitor = await ApexCompetitor.findOne({
      sessionId: this.currentSession._id,
      competitorId
    });

    if (!existingCompetitor) {
      if (this.config.enableLogging) {
        console.log(`❌ Competitor ${competitorId} not found - skipping column update (c${columnId}: ${type} = ${value})`);
        console.log(`❌ Teams/competitors must be created from init message with grid data first!`);
      }
      return;
    }

    if (this.config.enableLogging) {
      console.log(`✅ Competitor ${competitorId} found, calling handleDriverFieldUpdate`);
    }

    try {
      // Process field update for existing competitor only
      await this.handleDriverFieldUpdate(competitorId, columnId, type, value);
    } catch (error) {
      console.error('Error handling driver message:', error);
    }
  }

  /**
   * Handle driver field updates based on type and value patterns
   */
  private async handleDriverFieldUpdate(competitorId: string, columnId: string, type: string, value: string): Promise<void> {
    // Verify competitor exists before processing any updates
    const competitor = await ApexCompetitor.findOne({
      sessionId: this.currentSession?._id,
      competitorId
    });

    if (!competitor) {
      if (this.config.enableLogging) {
        console.log(`❌ Competitor ${competitorId} not found for field update - skipping`);
      }
      return;
    }

    // ALWAYS FOLLOW THIS RULE: Use grid header data types to determine field meaning
    // Map column ID to grid header type - this is the authoritative source
    let gridColumnType = null;
    if (this.gridData && this.gridData.header_types) {
      // Try with 'c' prefix first (e.g., 'c9'), then without (e.g., '9')
      const columnKey = `c${columnId}`;
      if (this.gridData.header_types[columnKey]) {
        gridColumnType = this.gridData.header_types[columnKey];
      } else if (this.gridData.header_types[columnId]) {
        gridColumnType = this.gridData.header_types[columnId];
      }
    }

    if (this.config.enableLogging) {
      console.log(`📍 Column ${columnId}: message type="${type}", grid type="${gridColumnType}", value="${value}"`);
    }

    // If we don't have grid data, we can't properly parse - skip
    if (!gridColumnType) {
      if (this.config.enableLogging) {
        console.warn(`⚠️ No grid header type found for column ${columnId} - skipping`);
      }
      return;
    }

    if (this.config.enableLogging) {
      console.log(`🔄 Processing field update for competitor ${competitorId}: column ${columnId} (${gridColumnType}) = "${value}"`);
    }

    // Determine what kind of update this is based on GRID COLUMN TYPE (not message type)
    // Use grid header types to identify lap time columns
    if (gridColumnType === 'llp' || gridColumnType === 'blp') {
      // llp = last lap, blp = best lap
      if (this.config.enableLogging) {
        console.log(`🏁 Detected lap time field: ${gridColumnType} = "${value}"`);
      }
      if (value && value.includes(':')) {
        if (this.config.enableLogging) {
          console.log(`⏱️ Processing lap time: ${value}`);
        }
        await this.handleLapTimeUpdate(competitorId, value, gridColumnType);
      } else {
        if (this.config.enableLogging) {
          console.log(`⚠️ Invalid lap time format: "${value}"`);
        }
      }
    } else if (gridColumnType === 'pit' || gridColumnType === 'otr') {
      // pit = pit count, otr = on track time - these can indicate pit status
      await this.handlePitUpdate(competitorId, value, gridColumnType);
    } else if (gridColumnType === 'rk') {
      // rk = rank/position
      await this.handlePositionUpdate(competitorId, value);
    } else if (gridColumnType === 'tlp') {
      // tlp = total laps
      await this.handleLapUpdate(competitorId, value);
    } else if (gridColumnType === 'gap' || gridColumnType === 'int') {
      // gap = gap to leader, int = interval to car ahead
      if (this.config.enableLogging) {
        console.log(`Competitor ${competitorId}: ${gridColumnType === 'gap' ? 'Gap' : 'Interval'} = ${value}`);
      }
    } else if (gridColumnType === 'sta') {
      // sta = status
      await this.handleStatusUpdate(competitorId, value, gridColumnType);
    } else {
      // Generic field update - log for debugging
      if (this.config.enableLogging) {
        console.log(`Competitor ${competitorId}: Column ${columnId} (grid: ${gridColumnType}, msg: ${type}) = ${value}`);
      }
    }
  }

  /**
   * Handle pit-related updates
   */
  private async handlePitUpdate(competitorId: string, value: string, type: string): Promise<void> {
    if (!this.currentSession) return;

    try {
      // Get competitor info for references
      const competitor = await ApexCompetitor.findOne({
        sessionId: this.currentSession._id,
        competitorId
      });

      if (!competitor) {
        if (this.config.enableLogging) {
          console.warn(`❌ Competitor ${competitorId} not found for pit update`);
        }
        return;
      }

      if (this.config.enableLogging) {
        console.log(`🏁 Pit update - Competitor ${competitorId}: ${type} = ${value}`);
      }

      // Handle different pit-related column types
      if (type === 'pit') {
        // Pit count - could indicate pit entry/exit
        const pitCount = parseInt(value) || 0;

        // Update team pit count
        await ApexTeam.updateOne(
          { sessionId: this.currentSession._id, teamId: competitor.teamId },
          { pitCount: pitCount, updatedAt: new Date() }
        );

      } else if (type === 'otr') {
        // On track time - could indicate if car is on track or in pits
        if (value && value.includes(':')) {
          // Car is on track
          await this.handlePitExit(competitorId, competitor);
        }

      } else if (type === 'ib') {
        // In box - direct pit status indicator
        if (value === '1' || value === 'true' || value === 'in') {
          await this.handlePitEntry(competitorId, competitor);
        } else if (value === '0' || value === 'false' || value === 'out') {
          await this.handlePitExit(competitorId, competitor);
        }
      }

    } catch (error) {
      console.error('Error handling pit update:', error);
    }
  }

  /**
   * Handle pit entry
   */
  private async handlePitEntry(competitorId: string, competitor: any): Promise<void> {
    if (!this.currentSession) return;

    try {
      // Check if there's already an active pit stop
      const existingPitStop = await ApexPitStop.findOne({
        sessionId: this.currentSession._id,
        competitorId,
        isActive: true
      });

      if (!existingPitStop) {
        const pitStop = new ApexPitStop({
          sessionId: this.currentSession._id,
          competitorId,
          teamId: competitor.teamId,
          kartNumber: competitor.kartNumber,
          pitInTime: new Date(),
          lapNumber: await ApexLap.countDocuments({
            sessionId: this.currentSession._id,
            competitorId
          }),
          reason: 'Regular',
          isActive: true
        });

        await pitStop.save();

        // Update team status
        await ApexTeam.updateOne(
          { sessionId: this.currentSession._id, teamId: competitor.teamId },
          { status: 'pit', updatedAt: new Date() }
        );

        if (this.config.enableLogging) {
          console.log(`🏁 Pit entry recorded - Competitor ${competitorId}`);
        }

        this.emit('pitStopRecorded', {
          competitorId,
          teamId: competitor.teamId,
          kartNumber: competitor.kartNumber,
          pitInTime: pitStop.pitInTime,
          type: 'entry'
        });
      }
    } catch (error) {
      console.error('Error handling pit entry:', error);
    }
  }

  /**
   * Handle pit exit
   */
  private async handlePitExit(competitorId: string, competitor: any): Promise<void> {
    if (!this.currentSession) return;

    try {
      // Find active pit stop
      const activePitStop = await ApexPitStop.findOne({
        sessionId: this.currentSession._id,
        competitorId,
        isActive: true
      });

      if (activePitStop) {
        activePitStop.pitOutTime = new Date();
        activePitStop.pitDuration = (activePitStop.pitOutTime.getTime() - activePitStop.pitInTime.getTime()) / 1000;
        activePitStop.isActive = false;
        await activePitStop.save();

        // Update team status
        await ApexTeam.updateOne(
          { sessionId: this.currentSession._id, teamId: competitor.teamId },
          { status: 'active', updatedAt: new Date() }
        );

        if (this.config.enableLogging) {
          console.log(`🏁 Pit exit recorded - Competitor ${competitorId}, duration: ${activePitStop.pitDuration}s`);
        }

        this.emit('pitStopRecorded', {
          competitorId,
          teamId: competitor.teamId,
          kartNumber: competitor.kartNumber,
          pitInTime: activePitStop.pitInTime,
          pitOutTime: activePitStop.pitOutTime,
          pitDuration: activePitStop.pitDuration,
          type: 'exit'
        });
      }
    } catch (error) {
      console.error('Error handling pit exit:', error);
    }
  }

  /**
   * Handle lap count updates
   */
  private async handleLapUpdate(competitorId: string, lapCount: string): Promise<void> {
    // Implementation for lap updates
    if (this.config.enableLogging) {
      console.log(`Competitor ${competitorId}: Lap count ${lapCount}`);
    }
  }

  /**
   * Handle lap time updates
   */
  private async handleLapTimeUpdate(competitorId: string, lapTime: string, type: string): Promise<void> {
    if (!this.currentSession) return;

    try {
      const lapTimeSeconds = this.parseTimeToSeconds(lapTime);

      if (lapTimeSeconds <= 0) {
        if (this.config.enableLogging) {
          console.warn(`Invalid lap time for competitor ${competitorId}: ${lapTime}`);
        }
        return;
      }

      // Get competitor info for references - must exist from init message grid parsing
      const competitor = await ApexCompetitor.findOne({
        sessionId: this.currentSession._id,
        competitorId
      });

      if (!competitor) {
        if (this.config.enableLogging) {
          console.warn(`❌ Competitor ${competitorId} not found for lap time - teams must be created from init message with grid data first`);
        }
        return;
      }

      // Get current lap count for this competitor
      const currentLapCount = await ApexLap.countDocuments({
        sessionId: this.currentSession._id,
        competitorId
      });

      const isBestLap = type === "tb" || type === "TB";

      // Create lap record with all references
      const lap = new ApexLap({
        sessionId: this.currentSession._id,
        competitorId,
        teamId: competitor.teamId,
        kartNumber: competitor.kartNumber,
        lapNumber: currentLapCount + 1,
        lapTime: lapTimeSeconds,
        lapTimeFormatted: lapTime,
        timestamp: new Date(),
        isBestLap,
        isPersonalBest: false // Will be calculated below
      });

      // Check if this is a personal best
      const personalBest = await ApexLap.findOne({
        sessionId: this.currentSession._id,
        competitorId
      }).sort({ lapTime: 1 });

      if (!personalBest || lapTimeSeconds < personalBest.lapTime) {
        lap.isPersonalBest = true;
      }

      await lap.save();

      // Update team statistics
      await this.updateTeamStatistics(competitor.teamId, lapTimeSeconds, lapTime, isBestLap);

      if (this.config.enableLogging) {
        console.log(`✅ Lap recorded - Competitor ${competitorId}: ${lapTime} (${lapTimeSeconds}s) ${isBestLap ? '[BEST]' : ''} ${lap.isPersonalBest ? '[PB]' : ''}`);
      }

      // Emit lap recorded event
      this.emit('lapRecorded', {
        competitorId,
        teamId: competitor.teamId,
        kartNumber: competitor.kartNumber,
        lapNumber: lap.lapNumber,
        lapTime: lapTimeSeconds,
        lapTimeFormatted: lapTime,
        isBestLap,
        isPersonalBest: lap.isPersonalBest,
        timestamp: new Date()
      });

    } catch (error) {
      console.error('Error saving lap time:', error);
    }
  }

  /**
   * Update team statistics when a new lap is recorded
   */
  private async updateTeamStatistics(teamId: string, lapTimeSeconds: number, lapTimeFormatted: string, isBestLap: boolean): Promise<void> {
    if (!this.currentSession) return;

    try {
      const team = await ApexTeam.findOne({
        sessionId: this.currentSession._id,
        teamId
      });

      if (!team) return;

      // Update total laps
      team.totalLaps = (team.totalLaps || 0) + 1;

      // Update last lap time
      team.lastLapTime = lapTimeSeconds;
      team.lastLapTimeFormatted = lapTimeFormatted;

      // Update best lap time if this is better
      if (!team.bestLapTime || lapTimeSeconds < team.bestLapTime || isBestLap) {
        team.bestLapTime = lapTimeSeconds;
        team.bestLapTimeFormatted = lapTimeFormatted;
      }

      team.updatedAt = new Date();
      await team.save();

      if (this.config.enableLogging) {
        console.log(`📊 Team ${teamId} stats updated: ${team.totalLaps} laps, best: ${team.bestLapTimeFormatted}`);
      }

    } catch (error) {
      console.error('Error updating team statistics:', error);
    }
  }

  /**
   * Handle pit status updates
   */
  private async handlePitStatusUpdate(competitorId: string, status: string): Promise<void> {
    if (!this.currentSession) return;

    try {
      // Get competitor info for references
      const competitor = await ApexCompetitor.findOne({
        sessionId: this.currentSession._id,
        competitorId
      });

      if (!competitor) return;

      // Create or update pit stop record
      if (status === 'in' || status === 'pit') {
        // Pit entry
        const existingPitStop = await ApexPitStop.findOne({
          sessionId: this.currentSession._id,
          competitorId,
          isActive: true
        });

        if (!existingPitStop) {
          const pitStop = new ApexPitStop({
            sessionId: this.currentSession._id,
            competitorId,
            teamId: competitor.teamId,
            kartNumber: competitor.kartNumber,
            pitInTime: new Date(),
            lapNumber: await ApexLap.countDocuments({
              sessionId: this.currentSession._id,
              competitorId
            }),
            reason: 'Regular',
            isActive: true
          });

          await pitStop.save();

          if (this.config.enableLogging) {
            console.log(`🏁 Pit entry - Competitor ${competitorId}`);
          }

          this.emit('pitStopRecorded', {
            competitorId,
            teamId: competitor.teamId,
            kartNumber: competitor.kartNumber,
            pitInTime: pitStop.pitInTime,
            type: 'entry'
          });
        }
      } else if (status === 'out' || status === 'track') {
        // Pit exit
        const activePitStop = await ApexPitStop.findOne({
          sessionId: this.currentSession._id,
          competitorId,
          isActive: true
        });

        if (activePitStop) {
          activePitStop.pitOutTime = new Date();
          activePitStop.pitDuration = (activePitStop.pitOutTime.getTime() - activePitStop.pitInTime.getTime()) / 1000;
          activePitStop.isActive = false;
          await activePitStop.save();

          if (this.config.enableLogging) {
            console.log(`🏁 Pit exit - Competitor ${competitorId}, duration: ${activePitStop.pitDuration}s`);
          }

          this.emit('pitStopRecorded', {
            competitorId,
            teamId: competitor.teamId,
            kartNumber: competitor.kartNumber,
            pitInTime: activePitStop.pitInTime,
            pitOutTime: activePitStop.pitOutTime,
            pitDuration: activePitStop.pitDuration,
            type: 'exit'
          });
        }
      }

      // Update team status
      await ApexTeam.updateOne(
        { sessionId: this.currentSession._id, teamId: competitor.teamId },
        { status: status === 'pit' || status === 'in' ? 'pit' : 'active' }
      );

    } catch (error) {
      console.error('Error handling pit status update:', error);
    }
  }

  /**
   * Handle status updates
   */
  private async handleStatusUpdate(competitorId: string, status: string, type: string): Promise<void> {
    // Implementation for status updates
    if (this.config.enableLogging) {
      console.log(`Competitor ${competitorId}: Status ${status} (${type})`);
    }
  }

  /**
   * Handle position updates
   */
  private async handlePositionUpdate(competitorId: string, position: string): Promise<void> {
    if (!this.currentSession) return;

    try {
      const positionNum = parseInt(position);
      if (isNaN(positionNum)) return;

      // Get competitor info
      const competitor = await ApexCompetitor.findOne({
        sessionId: this.currentSession._id,
        competitorId
      });

      if (!competitor) return;

      // Update team position
      await ApexTeam.updateOne(
        { sessionId: this.currentSession._id, teamId: competitor.teamId },
        { position: positionNum, updatedAt: new Date() }
      );

      if (this.config.enableLogging) {
        console.log(`📍 Position update - Competitor ${competitorId}: P${positionNum}`);
      }

      this.emit('positionUpdate', {
        competitorId,
        teamId: competitor.teamId,
        position: positionNum
      });

    } catch (error) {
      console.error('Error handling position update:', error);
    }
  }

  /**
   * Generate a unique session ID
   */
  private generateSessionId(sessionData: Record<string, any>): string {
    const title1 = sessionData.title1?.value || 'race';
    const title2 = sessionData.title2?.value || 'session';
    const timestamp = Date.now();
    return `${title1}_${title2}_${timestamp}`.replace(/\s+/g, '_').toLowerCase();
  }

  /**
   * Parse time string to seconds
   */
  private parseTimeToSeconds(timeStr: string): number {
    if (!timeStr) return 0;

    // Handle format like "1:23.456"
    if (timeStr.includes(':')) {
      const parts = timeStr.split(':');
      if (parts.length >= 2) {
        const minutes = parseInt(parts[0] || '0') || 0;
        const seconds = parseFloat(parts[1] || '0') || 0;
        return minutes * 60 + seconds;
      }
    }

    // Handle direct seconds format
    return parseFloat(timeStr) || 0;
  }

  /**
   * Get current session
   */
  getCurrentSession() {
    return this.currentSession;
  }

  /**
   * Get grid data
   */
  getGridData() {
    return this.gridData;
  }
}

export default ApexParser;
