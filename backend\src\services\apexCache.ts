/**
 * High-performance caching service for Apex racing data
 * Optimized for fast websocket message processing
 */

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

interface CompetitorInfo {
  competitorId: string;
  teamId: string;
  kartNumber: number;
  name: string;
}

interface SessionInfo {
  sessionId: string;
  gridData: any;
  headerTypes: Record<string, string>;
}

class ApexCache {
  private competitors = new Map<string, CacheEntry<CompetitorInfo>>();
  private sessions = new Map<string, CacheEntry<SessionInfo>>();
  private lapCounts = new Map<string, CacheEntry<number>>();
  private bestLaps = new Map<string, CacheEntry<number>>();
  
  // Cache TTL in milliseconds
  private readonly COMPETITOR_TTL = 5 * 60 * 1000; // 5 minutes
  private readonly SESSION_TTL = 30 * 60 * 1000;   // 30 minutes
  private readonly LAP_COUNT_TTL = 10 * 1000;      // 10 seconds
  private readonly BEST_LAP_TTL = 30 * 1000;       // 30 seconds

  /**
   * Get competitor info from cache or database
   */
  async getCompetitor(sessionId: string, competitorId: string): Promise<CompetitorInfo | null> {
    const cacheKey = `${sessionId}:${competitorId}`;
    const cached = this.competitors.get(cacheKey);
    
    // Check if cache is valid
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached.data;
    }
    
    // Fetch from database
    try {
      const { ApexCompetitor } = await import('../models/ApexModels');
      const competitor = await ApexCompetitor.findOne({
        sessionId,
        competitorId
      }).lean();
      
      if (competitor) {
        const competitorInfo: CompetitorInfo = {
          competitorId: competitor.competitorId,
          teamId: competitor.teamId,
          kartNumber: competitor.kartNumber,
          name: competitor.name
        };
        
        // Cache the result
        this.competitors.set(cacheKey, {
          data: competitorInfo,
          timestamp: Date.now(),
          ttl: this.COMPETITOR_TTL
        });
        
        return competitorInfo;
      }
    } catch (error) {
      console.error('Error fetching competitor from database:', error);
    }
    
    return null;
  }

  /**
   * Get session info from cache or database
   */
  async getSession(sessionId: string): Promise<SessionInfo | null> {
    const cached = this.sessions.get(sessionId);
    
    // Check if cache is valid
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached.data;
    }
    
    // Fetch from database
    try {
      const { ApexSession } = await import('../models/ApexModels');
      const session = await ApexSession.findById(sessionId).lean();
      
      if (session && session.gridData) {
        const sessionInfo: SessionInfo = {
          sessionId: session._id.toString(),
          gridData: session.gridData,
          headerTypes: session.gridData.header_types || {}
        };
        
        // Cache the result
        this.sessions.set(sessionId, {
          data: sessionInfo,
          timestamp: Date.now(),
          ttl: this.SESSION_TTL
        });
        
        return sessionInfo;
      }
    } catch (error) {
      console.error('Error fetching session from database:', error);
    }
    
    return null;
  }

  /**
   * Get lap count from cache or database
   */
  async getLapCount(sessionId: string, competitorId: string): Promise<number> {
    const cacheKey = `${sessionId}:${competitorId}:laps`;
    const cached = this.lapCounts.get(cacheKey);
    
    // Check if cache is valid
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached.data;
    }
    
    // Fetch from database
    try {
      const { ApexLap } = await import('../models/ApexModels');
      const count = await ApexLap.countDocuments({
        sessionId,
        competitorId
      });
      
      // Cache the result
      this.lapCounts.set(cacheKey, {
        data: count,
        timestamp: Date.now(),
        ttl: this.LAP_COUNT_TTL
      });
      
      return count;
    } catch (error) {
      console.error('Error fetching lap count from database:', error);
      return 0;
    }
  }

  /**
   * Get best lap time from cache or database
   */
  async getBestLapTime(sessionId: string, competitorId: string): Promise<number | null> {
    const cacheKey = `${sessionId}:${competitorId}:best`;
    const cached = this.bestLaps.get(cacheKey);
    
    // Check if cache is valid
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached.data;
    }
    
    // Fetch from database
    try {
      const { ApexLap } = await import('../models/ApexModels');
      const bestLap = await ApexLap.findOne({
        sessionId,
        competitorId
      }).sort({ lapTime: 1 }).lean();
      
      const bestTime = bestLap?.lapTime || null;
      
      // Cache the result
      this.bestLaps.set(cacheKey, {
        data: bestTime,
        timestamp: Date.now(),
        ttl: this.BEST_LAP_TTL
      });
      
      return bestTime;
    } catch (error) {
      console.error('Error fetching best lap time from database:', error);
      return null;
    }
  }

  /**
   * Invalidate competitor cache
   */
  invalidateCompetitor(sessionId: string, competitorId: string): void {
    const cacheKey = `${sessionId}:${competitorId}`;
    this.competitors.delete(cacheKey);
    this.lapCounts.delete(`${cacheKey}:laps`);
    this.bestLaps.delete(`${cacheKey}:best`);
  }

  /**
   * Invalidate session cache
   */
  invalidateSession(sessionId: string): void {
    this.sessions.delete(sessionId);
  }

  /**
   * Update lap count cache (increment)
   */
  incrementLapCount(sessionId: string, competitorId: string): void {
    const cacheKey = `${sessionId}:${competitorId}:laps`;
    const cached = this.lapCounts.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      cached.data += 1;
      cached.timestamp = Date.now();
    }
  }

  /**
   * Update best lap cache
   */
  updateBestLap(sessionId: string, competitorId: string, lapTime: number): void {
    const cacheKey = `${sessionId}:${competitorId}:best`;
    const cached = this.bestLaps.get(cacheKey);
    
    if (!cached || lapTime < cached.data) {
      this.bestLaps.set(cacheKey, {
        data: lapTime,
        timestamp: Date.now(),
        ttl: this.BEST_LAP_TTL
      });
    }
  }

  /**
   * Clear expired cache entries
   */
  cleanup(): void {
    const now = Date.now();
    
    // Clean competitors cache
    for (const [key, entry] of this.competitors.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.competitors.delete(key);
      }
    }
    
    // Clean sessions cache
    for (const [key, entry] of this.sessions.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.sessions.delete(key);
      }
    }
    
    // Clean lap counts cache
    for (const [key, entry] of this.lapCounts.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.lapCounts.delete(key);
      }
    }
    
    // Clean best laps cache
    for (const [key, entry] of this.bestLaps.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.bestLaps.delete(key);
      }
    }
  }

  /**
   * Get cache statistics
   */
  getStats() {
    return {
      competitors: this.competitors.size,
      sessions: this.sessions.size,
      lapCounts: this.lapCounts.size,
      bestLaps: this.bestLaps.size,
      total: this.competitors.size + this.sessions.size + this.lapCounts.size + this.bestLaps.size
    };
  }

  /**
   * Clear all cache
   */
  clear(): void {
    this.competitors.clear();
    this.sessions.clear();
    this.lapCounts.clear();
    this.bestLaps.clear();
  }
}

// Global cache instance
const apexCache = new ApexCache();

// Cleanup expired entries every 5 minutes
setInterval(() => {
  apexCache.cleanup();
}, 5 * 60 * 1000);

export default apexCache;
