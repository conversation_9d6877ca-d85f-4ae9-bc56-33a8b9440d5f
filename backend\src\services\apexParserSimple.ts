import { EventEmitter } from 'events';
import mongoose from 'mongoose';
import { parseLogMessage } from './apexMessageParser';
import { parseGridData, GridParseResult } from './apexGridParser';
import { ApexSession, ApexTeam, ApexKart, ApexCompetitor, ApexLap } from '../models/ApexModels';

export interface ApexParserConfig {
  sessionId?: string;
  raceId?: string;
  enableLogging?: boolean;
}

/**
 * Simplified Apex Parser
 * 
 * Logic:
 * 1. Parse socket message
 * 2. If grid is found -> parse and create database elements
 * 3. If grid not found -> update existing database elements for old parsed grid
 * 4. Parse all received lines
 */
export class ApexParserSimple extends EventEmitter {
  private currentSession: any = null;
  private gridData: GridParseResult | null = null;
  private config: ApexParserConfig;
  private websocketToCompetitorMap: Map<string, mongoose.Types.ObjectId> = new Map(); // Map websocket ID to competitor ObjectId

  constructor(config: ApexParserConfig = {}) {
    super();
    this.config = {
      enableLogging: true,
      ...config
    };
  }

  /**
   * Parse log content (can be single line or multi-line)
   */
  async parseLogContent(content: string): Promise<void> {
    try {
      // Split content into lines and process each line
      const lines = content.split('\n').filter(line => line.trim().length > 0);
      
      if (this.config.enableLogging) {
        console.log(`📝 Processing ${lines.length} lines`);
      }

      for (const line of lines) {
        await this.parseMessage(line);
      }
    } catch (error) {
      console.error('Error parsing log content:', error);
      throw error;
    }
  }

  /**
   * Parse a single websocket message
   */
  async parseMessage(rawMessage: string): Promise<void> {
    try {
      if (this.config.enableLogging) {
        console.log('📨 Parsing message:', rawMessage.substring(0, 100) + '...');
      }

      // Parse the message
      const message = parseLogMessage(rawMessage);
      
      if (!message || !message.data) {
        if (this.config.enableLogging) {
          console.warn('⚠️ Unknown message format, logging original message:');
          console.warn('📝 Original message:', rawMessage);
        }
        return;
      }

      // Emit raw message event
      this.emit('messageReceived', { raw: rawMessage, parsed: message });

      // Simple logic: check if grid is present
      if (message.data.grid) {
        // Grid found -> parse and create database elements
        if (this.config.enableLogging) {
          console.log('🏁 Grid found - creating database elements');
        }
        await this.handleGridMessage(message.data);
      } else {
        // No grid -> update existing database elements
        if (this.config.enableLogging) {
          console.log(`🔄 No grid - updating existing elements (${Object.keys(message.data).length} fields)`);
        }
        await this.handleUpdateMessage(message.data);
      }
    } catch (error) {
      console.error('Error parsing message:', error);
      console.error('📝 Original message that caused error:', rawMessage);
    }
  }

  /**
   * Handle message with grid data - create database elements
   */
  private async handleGridMessage(messageData: Record<string, any>): Promise<void> {
    try {
      // Parse grid data
      if (messageData.grid) {
        if (this.config.enableLogging) {
          console.log('📊 Parsing grid data...');
        }
        this.gridData = parseGridData(messageData.grid.value);
        if (this.config.enableLogging) {
          console.log(`   Parsed ${Object.keys(this.gridData.drivers).length} drivers from grid`);
        }
      }

      // Create or update session
      await this.createSession(messageData);

      // Create teams, karts, and competitors from grid data
      if (this.gridData && this.currentSession) {
        await this.createEntitiesFromGrid();
      }

    } catch (error) {
      console.error('Error handling grid message:', error);
    }
  }

  /**
   * Handle message without grid data - update existing database elements
   */
  private async handleUpdateMessage(messageData: Record<string, any>): Promise<void> {
    try {
      // Skip updates if no session with grid elements exists yet
      if (!this.currentSession || !this.gridData) {
        if (this.config.enableLogging) {
          console.log('⚠️ Skipping updates - no session with grid elements found yet. Waiting for init message with grid.');
        }
        return;
      }

      // Process all fields as updates
      await this.processUpdates(messageData);

    } catch (error) {
      console.error('Error handling update message:', error);
    }
  }

  /**
   * Create session from message data
   */
  private async createSession(messageData: Record<string, any>): Promise<void> {
    try {
      const sessionData = {
        title1: messageData.title1?.value || 'Race Session',
        title2: messageData.title2?.value || new Date().toISOString(),
        track: messageData.track?.value || 'Unknown Track',
        isActive: true,
        gridData: this.gridData || {},
        sessionData: messageData || {}
      };

      // Check if session already exists
      if (this.config.sessionId) {
        this.currentSession = await ApexSession.findById(this.config.sessionId);
      }

      if (!this.currentSession) {
        this.currentSession = await ApexSession.create(sessionData);
        if (this.config.enableLogging) {
          console.log(`✅ Created session: ${this.currentSession._id}`);
        }
      } else {
        // Populate websocket mapping for existing session
        await this.populateWebsocketMapping();
        await ApexSession.findByIdAndUpdate(this.currentSession._id, {
          ...sessionData,
          updatedAt: new Date()
        });
        if (this.config.enableLogging) {
          console.log(`✅ Updated session: ${this.currentSession._id}`);
        }
      }

      this.emit('sessionCreated', this.currentSession);
    } catch (error) {
      console.error('Error creating session:', error);
    }
  }

  /**
   * Create default session when none exists
   */
  private async createDefaultSession(): Promise<void> {
    try {
      const now = new Date();
      const sessionData = {
        raceId: this.config.raceId || `default_race_${Date.now()}`, // Required field
        title1: 'Default Session',
        title2: now.toISOString(),
        track: 'Unknown Track',
        startTime: now, // Required field
        isActive: true,
        gridData: {},
        sessionData: {},
        lastUpdated: now,
        createdAt: now,
        updatedAt: now
      };

      this.currentSession = await ApexSession.create(sessionData);
      if (this.config.enableLogging) {
        console.log(`✅ Created default session: ${this.currentSession._id} (raceId: ${sessionData.raceId})`);
      }
    } catch (error) {
      console.error('Error creating default session:', error);
    }
  }

  /**
   * Populate websocket mapping from existing competitors
   */
  private async populateWebsocketMapping(): Promise<void> {
    if (!this.currentSession) return;

    try {
      const existingCompetitors = await ApexCompetitor.find({
        sessionId: this.currentSession._id
      }).lean();

      // Clear existing mapping first
      this.websocketToCompetitorMap.clear();

      // Populate the mapping
      existingCompetitors.forEach(competitor => {
        this.websocketToCompetitorMap.set(competitor.websocketId, competitor._id as mongoose.Types.ObjectId);
      });

      if (this.config.enableLogging) {
        console.log(`📋 Populated websocket mapping for ${existingCompetitors.length} existing competitors`);
        console.log(`   Session ID: ${this.currentSession._id}`);
        if (existingCompetitors.length > 0) {
          const sampleIds = existingCompetitors.slice(0, 5).map(c => c.websocketId).join(', ');
          console.log(`   Sample websocket IDs: ${sampleIds}`);
          console.log(`   Mapping size: ${this.websocketToCompetitorMap.size}`);
        } else {
          console.log(`   No competitors found in session ${this.currentSession._id}`);
        }
      }
    } catch (error) {
      console.error('Error populating websocket mapping:', error);
    }
  }

  /**
   * Create teams, karts, and competitors from grid data
   */
  private async createEntitiesFromGrid(): Promise<void> {
    if (!this.gridData || !this.currentSession) return;

    try {
      const sessionId = this.currentSession._id;

      // Populate websocket mapping from existing competitors
      await this.populateWebsocketMapping();

      // Check for existing entities to avoid duplicates
      const existingCompetitors = await ApexCompetitor.find({ sessionId }).lean();
      const existingKarts = await ApexKart.find({ sessionId }).lean();

      const existingWebsocketIds = new Set(existingCompetitors.map(c => c.websocketId));
      const existingKartNumbers = new Set(existingKarts.map(k => k.kartNumber));

      const teamsToCreate: any[] = [];
      const kartsToCreate: any[] = [];
      const competitorsToCreate: any[] = [];

      // First pass: Create teams and karts
      for (const [driverId, driverData] of Object.entries(this.gridData.drivers)) {
        const competitorId = driverId.replace('r', '');
        const teamName = driverData.dr?.value || `Team ${competitorId}`;
        const kartNumber = parseInt(driverData.no?.value || '0') || 0;
        const nationality = driverData.nat?.value || '';

        if (this.config.enableLogging && Object.keys(this.gridData.drivers).indexOf(driverId) < 3) {
          console.log(`📝 Sample driver ${driverId}: name="${teamName}", kart=${kartNumber}, nationality="${nationality}"`);
        }

        if (kartNumber > 0) {
          // Create team (if not exists)
          teamsToCreate.push({
            sessionId,
            name: teamName,
            currentKartId: null, // Will be set after kart creation
            pastKarts: [],
            pits: [],
            drivers: [teamName], // Add team name as driver
            nationality,
            totalLaps: 0,
            status: 'active',
            isActive: true
          });

          // Create kart (if not exists)
          if (!existingKartNumbers.has(kartNumber)) {
            kartsToCreate.push({
              sessionId,
              kartNumber,
              speed: 4,
              currentTeamId: null, // Will be set after team creation
              currentRowId: null,
              status: 'available',
              isActive: true
            });
          }
        } else if (this.config.enableLogging) {
          console.warn(`⚠️ Skipping driver ${driverId} - invalid kart number: ${kartNumber} (raw: "${driverData.no?.value}")`);
        }
      }

      // Bulk create teams first
      let createdTeams: any[] = [];
      if (teamsToCreate.length > 0) {
        createdTeams = await ApexTeam.insertMany(teamsToCreate);
        if (this.config.enableLogging) {
          console.log(`✅ Created ${createdTeams.length} new teams`);
        }
      }

      // Bulk create karts
      let createdKarts: any[] = [];
      if (kartsToCreate.length > 0) {
        createdKarts = await ApexKart.insertMany(kartsToCreate);
        if (this.config.enableLogging) {
          console.log(`✅ Created ${createdKarts.length} new karts`);
        }
      }

      // Second pass: Create competitors with proper ObjectId references
      // Create maps to find teams and karts by their properties
      const teamsByName = new Map();
      const kartsByNumber = new Map();

      // Map created teams by name
      createdTeams.forEach(team => {
        teamsByName.set(team.name, team);
      });

      // Map created karts by number
      createdKarts.forEach(kart => {
        kartsByNumber.set(kart.kartNumber, kart);
      });

      if (this.config.enableLogging) {
        console.log(`📋 Team mapping: ${teamsByName.size} teams, Kart mapping: ${kartsByNumber.size} karts`);
      }

      let processedCount = 0;
      let validKartCount = 0;
      let matchedCount = 0;

      for (const [driverId, driverData] of Object.entries(this.gridData.drivers)) {
        const competitorId = driverId.replace('r', '');
        const teamName = driverData.dr?.value || `Team ${competitorId}`;
        const kartNumber = parseInt(driverData.no?.value || '0') || 0;
        const nationality = driverData.nat?.value || '';

        processedCount++;

        if (kartNumber > 0) {
          validKartCount++;

          if (!existingWebsocketIds.has(competitorId)) {
            const team = teamsByName.get(teamName);
            const kart = kartsByNumber.get(kartNumber);

            if (team && kart) {
              matchedCount++;
              competitorsToCreate.push({
                sessionId,
                websocketId: competitorId, // Store websocket ID for mapping
                teamId: team._id, // ObjectId reference
                kartId: kart._id, // ObjectId reference
                name: teamName,
                nationality,
                drivers: [teamName],
                isActive: true
              });

              // Update team's currentKartId
              await ApexTeam.findByIdAndUpdate(team._id, { currentKartId: kart._id });
              // Update kart's currentTeamId
              await ApexKart.findByIdAndUpdate(kart._id, { currentTeamId: team._id });
            } else {
              if (this.config.enableLogging && matchedCount < 3) {
                console.warn(`⚠️ Could not find team "${teamName}" or kart ${kartNumber} for competitor ${competitorId}`);
                console.warn(`   Team found: ${!!team}, Kart found: ${!!kart}`);
                if (!team) console.warn(`   Available teams: ${Array.from(teamsByName.keys()).slice(0, 5).join(', ')}...`);
                if (!kart) console.warn(`   Available kart numbers: ${Array.from(kartsByNumber.keys()).slice(0, 10).join(', ')}...`);
              }
            }
          }
        } else if (this.config.enableLogging && processedCount < 3) {
          console.warn(`⚠️ Skipping competitor ${competitorId} - invalid kart number: ${kartNumber} (raw: "${driverData.no?.value}")`);
        }
      }

      if (this.config.enableLogging) {
        console.log(`📊 Competitor creation summary: processed=${processedCount}, validKarts=${validKartCount}, matched=${matchedCount}, toCreate=${competitorsToCreate.length}`);
      }

      // Bulk create competitors
      if (competitorsToCreate.length > 0) {
        const createdCompetitors = await ApexCompetitor.insertMany(competitorsToCreate);

        // Populate websocket to competitor ObjectId mapping
        createdCompetitors.forEach(competitor => {
          this.websocketToCompetitorMap.set(competitor.websocketId, competitor._id);
        });

        if (this.config.enableLogging) {
          console.log(`✅ Created ${competitorsToCreate.length} new competitors`);
          console.log(`📋 Sample competitors: ${createdCompetitors.slice(0, 3).map(c => `${c.websocketId}(${c.name})`).join(', ')}`);
          console.log(`📋 Websocket mapping populated with ${this.websocketToCompetitorMap.size} entries`);
        }
      } else {
        // Even if no new competitors were created, populate mapping from existing ones
        await this.populateWebsocketMapping();

        if (this.config.enableLogging) {
          console.warn('⚠️ No new competitors were created - using existing competitors');
          console.warn(`   Total drivers in grid: ${Object.keys(this.gridData.drivers).length}`);
          console.warn(`   Teams created: ${createdTeams.length}, Karts created: ${createdKarts.length}`);
          console.warn(`   Existing competitors: ${existingCompetitors.length}, Existing karts: ${existingKarts.length}`);
          console.warn(`   Websocket mapping size: ${this.websocketToCompetitorMap.size}`);
        }
      }

      // Create apex karts for pit rows
      try {
        const { ApexPitRowService } = await import('./apexPitRowService');
        await ApexPitRowService.createApexKartsForPitRows(sessionId.toString());
        if (this.config.enableLogging) {
          console.log('✅ Created apex karts for pit rows');
        }
      } catch (pitRowError) {
        console.error('Error creating apex karts for pit rows:', pitRowError);
      }

    } catch (error) {
      console.error('Error creating entities from grid:', error);
    }
  }

  /**
   * Process update messages for existing entities
   */
  private async processUpdates(messageData: Record<string, any>): Promise<void> {
    if (!this.currentSession) return;

    try {
      // Ensure websocket mapping is populated before processing updates
      if (this.websocketToCompetitorMap.size === 0) {
        await this.populateWebsocketMapping();
      }

      for (const [key, data] of Object.entries(messageData)) {
        // Convert data to expected format if needed
        const fieldData = typeof data === 'object' && data.value !== undefined
          ? data
          : { type: key, value: String(data) };

        if (key.startsWith('r') && key.includes('c')) {
          // Driver/competitor field (format: r{id}c{column})
          await this.handleDriverUpdate(key, fieldData);
        } else {
          // Session field or other
          await this.handleSessionUpdate(key, fieldData);
        }
      }
    } catch (error) {
      console.error('Error processing updates:', error);
    }
  }

  /**
   * Handle driver/competitor updates
   */
  private async handleDriverUpdate(key: string, data: any): Promise<void> {
    // Extract competitor ID and column from key (e.g., "r45393c4" -> competitorId: "45393", column: "c4")
    const match = key.match(/r(\d+)c(\d+)/);
    if (!match) return;

    const competitorId = match[1];
    const columnId = `c${match[2]}`;

    // Get the field type from grid header if available
    const fieldType = this.gridData?.header_types[columnId] || 'unknown';

    if (this.config.enableLogging) {
      console.log(`Updating competitor ${competitorId}, field ${fieldType}: ${data.value}`);
    }

    // Handle different field types
    switch (fieldType) {
      case 'llp': // Last lap time
      case 'blp': // Best lap time
        if (competitorId && data.value) {
          await this.handleLapTimeUpdate(competitorId, fieldType, data.value);
        }
        break;
      case 'sta': // Status
        if (competitorId && data.value) {
          await this.handleStatusUpdate(competitorId, data.value);
        }
        break;
      case 'pit': // Pit status
        if (competitorId && data.value) {
          await this.handlePitUpdate(competitorId, data.value);
        }
        break;
      default:
        // Generic field update
        break;
    }
  }

  /**
   * Handle session-level updates
   */
  private async handleSessionUpdate(key: string, data: any): Promise<void> {
    if (!this.currentSession) return;

    try {
      await ApexSession.findByIdAndUpdate(this.currentSession._id, {
        [key]: data.value,
        updatedAt: new Date()
      });
    } catch (error) {
      console.error(`Error updating session field ${key}:`, error);
    }
  }

  /**
   * Convert lap time string to milliseconds
   */
  private convertLapTimeToMs(lapTimeStr: string): number | null {
    if (!lapTimeStr || lapTimeStr === '') return null;

    try {
      // Handle formats like "1:11.328", "71.328", "1:11:328"
      const timeStr = lapTimeStr.trim();

      // Format: "1:11.328" (minutes:seconds.milliseconds)
      if (timeStr.includes(':') && timeStr.includes('.')) {
        const parts = timeStr.split(':');
        if (parts.length === 2 && parts[0] && parts[1]) {
          const minutes = parseInt(parts[0]);
          const secondsParts = parts[1].split('.');
          if (secondsParts.length === 2 && secondsParts[0] && secondsParts[1]) {
            const seconds = parseInt(secondsParts[0]);
            const milliseconds = parseInt(secondsParts[1].padEnd(3, '0').substring(0, 3));

            return (minutes * 60 * 1000) + (seconds * 1000) + milliseconds;
          }
        }
      }

      // Format: "71.328" (seconds.milliseconds)
      if (timeStr.includes('.') && !timeStr.includes(':')) {
        const parts = timeStr.split('.');
        if (parts.length === 2 && parts[0] && parts[1]) {
          const seconds = parseInt(parts[0]);
          const milliseconds = parseInt(parts[1].padEnd(3, '0').substring(0, 3));

          return (seconds * 1000) + milliseconds;
        }
      }

      // Format: just seconds as string
      const numericValue = parseFloat(timeStr);
      if (!isNaN(numericValue)) {
        return Math.round(numericValue * 1000); // Convert seconds to milliseconds
      }

      return null;
    } catch (error) {
      console.warn(`Error converting lap time "${lapTimeStr}":`, error);
      return null;
    }
  }

  /**
   * Handle lap time updates
   */
  private async handleLapTimeUpdate(websocketId: string, fieldType: string, lapTime: string): Promise<void> {
    if (!lapTime || lapTime === '') return;

    try {
      // Get competitor ObjectId from websocket mapping
      let competitorObjectId = this.websocketToCompetitorMap.get(websocketId);
      if (!competitorObjectId) {
        // Try to find competitor by websocketId if not in map
        const competitor = await ApexCompetitor.findOne({
          sessionId: this.currentSession._id,
          websocketId
        });

        if (competitor) {
          // Add to map for future use
          this.websocketToCompetitorMap.set(websocketId, competitor._id as mongoose.Types.ObjectId);
          competitorObjectId = competitor._id as mongoose.Types.ObjectId;
        } else {
          if (this.config.enableLogging) {
            console.warn(`⚠️ Competitor ${websocketId} not found for lap time update in session ${this.currentSession._id}`);
            const availableCompetitors = await this.getAvailableCompetitors();
            console.warn(`   Available competitors in session (${availableCompetitors.length}):`, availableCompetitors.slice(0, 10));
            console.warn(`   Websocket mapping size: ${this.websocketToCompetitorMap.size}`);
            console.warn(`   Skipping lap time update - competitor must be created from grid data first`);
          }
          return;
        }
      }

      // Find competitor to get team info
      const competitor = await ApexCompetitor.findById(competitorObjectId);
      if (!competitor) {
        if (this.config.enableLogging) {
          console.warn(`⚠️ Competitor ${websocketId} not found for lap time update`);
        }
        return;
      }

      // Convert lap time string to milliseconds (Number)
      const lapTimeMs = this.convertLapTimeToMs(lapTime);
      if (lapTimeMs === null) {
        if (this.config.enableLogging) {
          console.warn(`⚠️ Invalid lap time format: ${lapTime}`);
        }
        return;
      }

      // Create lap record
      const lapData = {
        sessionId: this.currentSession._id,
        competitorId: competitor._id, // ObjectId reference to competitor
        kartId: competitor.kartId, // ObjectId reference to kart
        lapNumber: 0, // TODO: Track lap numbers
        lapTime: lapTimeMs, // Required Number field (milliseconds)
        lapTimeFormatted: lapTime, // Keep original string format
        isBestLap: false, // TODO: Calculate if best lap
        isPersonalBest: false, // TODO: Calculate if personal best
        timestamp: new Date()
      };

      await ApexLap.create(lapData);

      if (this.config.enableLogging) {
        console.log(`📊 Lap time recorded: ${competitor.name} - ${lapTime} (${lapTimeMs}ms)`);
      }
    } catch (error) {
      console.error('Error handling lap time update:', error);
    }
  }

  /**
   * Handle status updates
   */
  private async handleStatusUpdate(websocketId: string, status: string): Promise<void> {
    try {
      // Get competitor ObjectId from websocket mapping
      let competitorObjectId = this.websocketToCompetitorMap.get(websocketId);
      if (!competitorObjectId) {
        // Try to find competitor by websocketId if not in map
        const competitor = await ApexCompetitor.findOne({
          sessionId: this.currentSession._id,
          websocketId
        });

        if (competitor) {
          // Add to map for future use
          this.websocketToCompetitorMap.set(websocketId, competitor._id as mongoose.Types.ObjectId);
          competitorObjectId = competitor._id as mongoose.Types.ObjectId;
        } else {
          if (this.config.enableLogging) {
            console.warn(`⚠️ Competitor ${websocketId} not found for status update in session ${this.currentSession._id}`);
            console.warn(`   Available competitors in session:`, await this.getAvailableCompetitors());
            console.warn(`   Skipping status update - competitor must be created from grid data first`);
          }
          return;
        }
      }

      // Find competitor to get kart info
      const competitor = await ApexCompetitor.findById(competitorObjectId);
      if (!competitor) return;

      // Update kart status
      await ApexKart.updateOne(
        {
          sessionId: this.currentSession._id,
          _id: competitor.kartId
        },
        {
          status: status === 'PIT' ? 'in_pit_row' : 'on_track',
          updatedAt: new Date()
        }
      );
    } catch (error) {
      console.error('Error handling status update:', error);
    }
  }

  /**
   * Handle pit updates
   */
  private async handlePitUpdate(websocketId: string, pitStatus: string): Promise<void> {
    // Similar to status update but specifically for pit events
    await this.handleStatusUpdate(websocketId, pitStatus);
  }



  /**
   * Get available competitors for debugging
   */
  private async getAvailableCompetitors(): Promise<string[]> {
    if (!this.currentSession) return [];

    try {
      const competitors = await ApexCompetitor.find({
        sessionId: this.currentSession._id
      }).select('websocketId name').lean();

      return competitors.map(c => `${c.websocketId} (${c.name})`);
    } catch (error) {
      console.error('Error fetching available competitors:', error);
      return [];
    }
  }

  /**
   * Get current session
   */
  getCurrentSession() {
    return this.currentSession;
  }

  /**
   * Get grid data
   */
  getGridData() {
    return this.gridData;
  }
}
