/**
 * Service for managing apex karts for pit rows
 * Creates apex karts that correspond to existing pit rows when using apex mode
 */

import { ApexKart, ApexSession } from '../models/ApexModels';
import { Row } from '../models/Row';
import { Kart } from '../models/Kart';
import mongoose from 'mongoose';

export class ApexPitRowService {
  
  /**
   * Create apex karts for all existing pit rows in a session
   * This ensures pit rows have corresponding apex karts when using apex mode
   */
  static async createApexKartsForPitRows(sessionId: string): Promise<void> {
    try {
      // Validate session exists
      const session = await ApexSession.findById(sessionId);
      if (!session) {
        throw new Error(`Apex session ${sessionId} not found`);
      }

      // Get all existing pit rows
      const pitRows = await Row.find({}).sort({ rowNumber: 1 });
      
      if (pitRows.length === 0) {
        console.log('No pit rows found to create apex karts for');
        return;
      }

      console.log(`Creating apex karts for ${pitRows.length} pit rows in session ${sessionId}`);

      // Check which apex karts already exist for this session
      const existingApexKarts = await ApexKart.find({
        sessionId: new mongoose.Types.ObjectId(sessionId)
      });

      const existingKartNumbers = new Set(existingApexKarts.map(k => k.kartNumber));

      const kartsToCreate = [];
      
      for (const row of pitRows) {
        // Generate a unique kart number for this pit row (use row number + 1000 to avoid conflicts)
        const pitRowKartNumber = 1000 + row.rowNumber;
        
        // Skip if apex kart already exists with this number
        if (existingKartNumbers.has(pitRowKartNumber)) {
          console.log(`Apex kart ${pitRowKartNumber} already exists for pit row ${row.rowNumber}`);
          continue;
        }

        // Create apex kart for this pit row
        kartsToCreate.push({
          sessionId: new mongoose.Types.ObjectId(sessionId),
          number: pitRowKartNumber,
          kartNumber: pitRowKartNumber,
          speed: 4, // Default speed level
          teamId: `pit_row_${row.rowNumber}`, // Special team ID for pit rows
          currentTeamId: null, // Not assigned to any team initially
          currentRowId: row._id, // Assign to this pit row
          currentDriverId: `pit_row_${row.rowNumber}`,
          status: 'in_pit_row', // Special status for pit row karts
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }

      if (kartsToCreate.length > 0) {
        const createdApexKarts = await ApexKart.insertMany(kartsToCreate);
        console.log(`✅ Created ${createdApexKarts.length} apex karts for pit rows`);

        // Update pit rows to reference the apex karts directly
        const rowUpdates = [];
        for (let i = 0; i < createdApexKarts.length; i++) {
          const apexKart = createdApexKarts[i];
          if (apexKart && apexKart.kartNumber) {
            const rowNumber = apexKart.kartNumber - 1000; // Extract row number from kart number

            rowUpdates.push({
              updateOne: {
                filter: { rowNumber: rowNumber },
                update: { $set: { currentKartId: apexKart._id } }
              }
            });
          }
        }

        if (rowUpdates.length > 0) {
          const { Row } = await import('../models/Row');
          await Row.bulkWrite(rowUpdates);
          console.log(`✅ Updated ${rowUpdates.length} pit rows with apex kart references`);
        }
      } else {
        console.log('All pit rows already have corresponding apex karts');
      }

    } catch (error) {
      console.error('Error creating apex karts for pit rows:', error);
      throw error;
    }
  }

  /**
   * Get apex kart for a specific pit row
   */
  static async getApexKartForPitRow(sessionId: string, rowNumber: number): Promise<any> {
    const pitRowKartNumber = 1000 + rowNumber;
    
    return await ApexKart.findOne({
      sessionId: new mongoose.Types.ObjectId(sessionId),
      kartNumber: pitRowKartNumber
    });
  }

  /**
   * Update apex kart assignment when pit row assignment changes
   */
  static async updateApexKartAssignment(
    sessionId: string, 
    rowNumber: number, 
    teamId: string | null
  ): Promise<void> {
    const pitRowKartNumber = 1000 + rowNumber;
    
    await ApexKart.findOneAndUpdate(
      {
        sessionId: new mongoose.Types.ObjectId(sessionId),
        kartNumber: pitRowKartNumber
      },
      {
        currentTeamId: teamId,
        status: teamId ? 'racing' : 'in_pit_row',
        updatedAt: new Date()
      }
    );
  }

  /**
   * Ensure pit rows don't contain non-apex karts when in apex mode
   */
  static async validatePitRowsForApexMode(sessionId: string): Promise<{ valid: boolean; issues: string[] }> {
    const issues: string[] = [];
    
    try {
      // Get all pit rows
      const pitRows = await Row.find({}).populate('currentKartId');
      
      // Get all apex karts for this session
      const apexKarts = await ApexKart.find({ 
        sessionId: new mongoose.Types.ObjectId(sessionId) 
      });
      
      const apexKartNumbers = new Set(apexKarts.map(k => k.kartNumber));
      
      for (const row of pitRows) {
        if (row.currentKartId) {
          const kart = row.currentKartId as any;
          
          // Check if this kart number exists in apex karts
          if (!apexKartNumbers.has(kart.number)) {
            issues.push(`Pit row ${row.rowNumber} contains kart #${kart.number} which is not an apex kart`);
          }
        }
      }
      
      return {
        valid: issues.length === 0,
        issues
      };
      
    } catch (error) {
      console.error('Error validating pit rows for apex mode:', error);
      return {
        valid: false,
        issues: [`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`]
      };
    }
  }
}
