#!/usr/bin/env ts-node

/**
 * TypeScript script to test grid parsing for both 24h Serres and Master Vittoria formats
 * This script will parse both log files and compare the grid structures
 */

import fs from 'fs';
import path from 'path';
import { parseGridData, GridParseResult } from '../src/services/apexGridParser';

// Function to extract grid data from log file
const extractGridFromLog = (logContent: string, raceName: string): GridParseResult | null => {
  console.log(`\n🔍 Extracting grid data from ${raceName}...`);
  
  // Find all grid entries
  const gridRegex = /grid\|\|([^]*?)(?=\n[^\s]|\n\n|\n$)/g;
  const grids: string[] = [];
  let match;
  
  while ((match = gridRegex.exec(logContent)) !== null) {
    grids.push(match[1].trim());
  }
  
  console.log(`   Found ${grids.length} grid entries`);
  
  if (grids.length === 0) {
    console.log(`   ❌ No grid data found in ${raceName}`);
    return null;
  }
  
  // Use the first grid entry for analysis
  const gridData = grids[0];
  console.log(`   📊 Using first grid entry (${gridData.length} characters)`);
  
  return parseGridData(gridData);
};

// Function to analyze parsed grid data
const analyzeGridData = (gridResult: GridParseResult, raceName: string): GridParseResult => {
  console.log(`\n📈 Analysis for ${raceName}:`);
  console.log(`   Drivers found: ${Object.keys(gridResult.drivers).length}`);
  console.log(`   Header columns: ${Object.keys(gridResult.header_types).length}`);
  
  // Show header mapping
  console.log(`\n   📋 Header mapping:`);
  for (const [column, type] of Object.entries(gridResult.header_types)) {
    const label = gridResult.header_labels[column] || 'N/A';
    console.log(`      ${column}: ${type} ("${label}")`);
  }
  
  // Show first few drivers
  console.log(`\n   👥 Sample drivers:`);
  const driverIds = Object.keys(gridResult.drivers).slice(0, 3);
  for (const driverId of driverIds) {
    const driver = gridResult.drivers[driverId];
    const name = driver.dr?.value || 'N/A';
    const kartNumber = driver.no?.value || 'N/A';
    const nationality = driver.nat?.value || 'N/A';
    console.log(`      ${driverId}: "${name}" (Kart: ${kartNumber}, Nation: ${nationality})`);
  }
  
  return gridResult;
};

// Function to compare grid structures
const compareGrids = (serresGrid: GridParseResult, vittoriaGrid: GridParseResult): void => {
  console.log(`\n🔄 Comparing grid structures:`);
  
  // Compare header types
  const serresHeaders = Object.keys(serresGrid.header_types);
  const vittoriaHeaders = Object.keys(vittoriaGrid.header_types);
  
  console.log(`\n   📊 Header columns:`);
  console.log(`      24h Serres: ${serresHeaders.length} columns`);
  console.log(`      Master Vittoria: ${vittoriaHeaders.length} columns`);
  
  // Find common and different headers
  const commonHeaders = serresHeaders.filter(h => vittoriaHeaders.includes(h));
  const serresOnly = serresHeaders.filter(h => !vittoriaHeaders.includes(h));
  const vittoriaOnly = vittoriaHeaders.filter(h => !serresHeaders.includes(h));
  
  console.log(`\n   🤝 Common headers (${commonHeaders.length}):`);
  commonHeaders.forEach(h => {
    const serresType = serresGrid.header_types[h];
    const vittoriaType = vittoriaGrid.header_types[h];
    const match = serresType === vittoriaType ? '✅' : '❌';
    console.log(`      ${h}: ${serresType} vs ${vittoriaType} ${match}`);
  });
  
  if (serresOnly.length > 0) {
    console.log(`\n   🔵 24h Serres only (${serresOnly.length}):`);
    serresOnly.forEach(h => {
      console.log(`      ${h}: ${serresGrid.header_types[h]} ("${serresGrid.header_labels[h]}")`);
    });
  }
  
  if (vittoriaOnly.length > 0) {
    console.log(`\n   🟡 Master Vittoria only (${vittoriaOnly.length}):`);
    vittoriaOnly.forEach(h => {
      console.log(`      ${h}: ${vittoriaGrid.header_types[h]} ("${vittoriaGrid.header_labels[h]}")`);
    });
  }
  
  // Check critical fields
  console.log(`\n   🎯 Critical field mapping:`);
  const criticalFields = ['no', 'dr', 'nat', 'llp', 'blp', 'rk', 'sta'];
  
  criticalFields.forEach(field => {
    const serresHas = Object.values(serresGrid.header_types).includes(field);
    const vittoriaHas = Object.values(vittoriaGrid.header_types).includes(field);
    const status = serresHas && vittoriaHas ? '✅' : serresHas ? '🔵' : vittoriaHas ? '🟡' : '❌';
    console.log(`      ${field}: ${status} (Serres: ${serresHas}, Vittoria: ${vittoriaHas})`);
  });
};

// Function to test competitor creation logic
const testCompetitorCreation = (gridResult: GridParseResult, raceName: string): void => {
  console.log(`\n🧪 Testing competitor creation logic for ${raceName}:`);
  
  const drivers = Object.entries(gridResult.drivers).slice(0, 3);
  
  drivers.forEach(([driverId, driverData]) => {
    const teamName = driverData.dr?.value || `Team ${driverId}`;
    const kartNumber = parseInt(driverData.no?.value || '0') || 0;
    const competitorId = driverId.replace('r', '');
    const nationality = driverData.nat?.value || 'Unknown';
    
    console.log(`   ${driverId} -> Competitor ${competitorId}:`);
    console.log(`      Team Name: "${teamName}"`);
    console.log(`      Kart Number: ${kartNumber}`);
    console.log(`      Nationality: "${nationality}"`);
    console.log(`      Valid: ${teamName !== 'N/A' && kartNumber > 0 ? '✅' : '❌'}`);
  });
};

// Main execution
const main = async (): Promise<void> => {
  console.log('🚀 Grid Parsing Test Script (TypeScript)');
  console.log('==========================================');
  
  const apexParserDir = path.join(__dirname, '..', 'apex parser files');
  const serresFile = path.join(apexParserDir, '24h serres.txt');
  const vittoriaFile = path.join(apexParserDir, 'master vitoria.txt');
  
  // Check if files exist
  if (!fs.existsSync(serresFile)) {
    console.error(`❌ File not found: ${serresFile}`);
    return;
  }
  
  if (!fs.existsSync(vittoriaFile)) {
    console.error(`❌ File not found: ${vittoriaFile}`);
    return;
  }
  
  try {
    // Read log files
    console.log('📖 Reading log files...');
    const serresContent = fs.readFileSync(serresFile, 'utf8');
    const vittoriaContent = fs.readFileSync(vittoriaFile, 'utf8');
    
    console.log(`   24h Serres: ${serresContent.length} characters`);
    console.log(`   Master Vittoria: ${vittoriaContent.length} characters`);
    
    // Extract and parse grid data
    const serresGrid = extractGridFromLog(serresContent, '24h Serres');
    const vittoriaGrid = extractGridFromLog(vittoriaContent, 'Master Vittoria');
    
    if (!serresGrid || !vittoriaGrid) {
      console.error('❌ Failed to extract grid data from one or both files');
      return;
    }
    
    // Analyze each grid
    analyzeGridData(serresGrid, '24h Serres');
    analyzeGridData(vittoriaGrid, 'Master Vittoria');
    
    // Compare grids
    compareGrids(serresGrid, vittoriaGrid);
    
    // Test competitor creation logic
    testCompetitorCreation(serresGrid, '24h Serres');
    testCompetitorCreation(vittoriaGrid, 'Master Vittoria');
    
    console.log('\n✅ Grid parsing test completed successfully!');
    console.log('\n📝 Summary:');
    console.log('   - Both race formats can be parsed using the same grid parser');
    console.log('   - The parser correctly uses data-type attributes to map fields');
    console.log('   - Competitors should be created properly from both formats');
    console.log('   - Any issues are likely in the init message processing or smart swap logic');
    
  } catch (error) {
    console.error('❌ Error during grid parsing test:', error);
  }
};

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

export { extractGridFromLog, analyzeGridData, compareGrids, testCompetitorCreation };
