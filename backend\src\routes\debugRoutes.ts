import express, { Request, Response } from 'express';
import mongoose from 'mongoose';
import { ApexSession, ApexTeam, ApexKart, ApexCompetitor, ApexLap, ApexPitStop, ApexLiveData } from '../models/ApexModels';

const router = express.Router();

// Helper function to find session by ObjectId or title
async function findSessionByIdOrTitle(sessionIdentifier: string) {
  // First try to find by ObjectId
  if (mongoose.Types.ObjectId.isValid(sessionIdentifier)) {
    const session = await ApexSession.findById(sessionIdentifier);
    if (session) return session;
  }

  // If not found or not a valid ObjectId, try to find by title combination
  const sessions = await ApexSession.find({
    $or: [
      { title1: { $regex: sessionIdentifier, $options: 'i' } },
      { title2: { $regex: sessionIdentifier, $options: 'i' } },
      { $expr: {
          $regexMatch: {
            input: { $concat: ['$title1', ' - ', '$title2'] },
            regex: sessionIdentifier,
            options: 'i'
          }
        }
      }
    ]
  }).sort({ createdAt: -1 });

  return sessions.length > 0 ? sessions[0] : null;
}

// Get database statistics
router.get('/stats', async (req: Request, res: Response) => {
  try {
    const stats = {
      sessions: await ApexSession.countDocuments(),
      teams: await ApexTeam.countDocuments(),
      karts: await ApexKart.countDocuments(),
      competitors: await ApexCompetitor.countDocuments(),
      laps: await ApexLap.countDocuments(),
      pitStops: await ApexPitStop.countDocuments(),
      liveData: await ApexLiveData.countDocuments(),
      timestamp: new Date().toISOString()
    };

    res.json(stats);
  } catch (error) {
    console.error('Error getting database stats:', error);
    res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to get database stats' 
    });
  }
});

// Get recent sessions
router.get('/sessions', async (req: Request, res: Response) => {
  try {
    const limit = parseInt(req.query.limit as string) || 10;
    
    const sessions = await ApexSession.find()
      .sort({ createdAt: -1 })
      .limit(limit)
      .select('_id raceId title1 title2 track startTime isActive createdAt');

    res.json(sessions);
  } catch (error) {
    console.error('Error getting sessions:', error);
    res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to get sessions' 
    });
  }
});

// Get session details with related data and relationships
router.get('/sessions/:sessionId/details', async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;

    // Find session by ObjectId or title
    const session = await findSessionByIdOrTitle(sessionId);

    if (!session) {
      res.status(404).json({ error: 'Session not found' });
      return;
    }

    // Use the session's _id for related queries
    const sessionObjectId = session._id;

    const [teams, karts, competitors, laps, pitStops] = await Promise.all([
      ApexTeam.find({ sessionId: sessionObjectId }).sort({ position: 1, kartNumber: 1 }),
      ApexKart.find({ sessionId: sessionObjectId }).sort({ kartNumber: 1 }),
      ApexCompetitor.find({ sessionId: sessionObjectId }).sort({ competitorId: 1 }),
      ApexLap.find({ sessionId: sessionObjectId }).sort({ timestamp: -1 }).limit(50),
      ApexPitStop.find({ sessionId: sessionObjectId }).sort({ pitInTime: -1 }).limit(20)
    ]);

    // Create relationship mappings
    const teamMap = new Map(teams.map(team => [team.teamId, team]));
    const kartMap = new Map(karts.map(kart => [kart.kartNumber, kart]));
    const competitorMap = new Map(competitors.map(comp => [comp.competitorId, comp]));

    // Enhance laps with team/kart info
    const enhancedLaps = laps.map(lap => {
      const competitor = competitorMap.get(lap.competitorId);
      const team = competitor ? teamMap.get(competitor.teamId) : null;
      return {
        ...lap.toObject(),
        competitorName: competitor?.name || `Competitor ${lap.competitorId}`,
        teamName: team?.name || `Team ${lap.teamId}`,
        kartNumber: lap.kartNumber
      };
    });

    // Enhance pit stops with team/kart info
    const enhancedPitStops = pitStops.map(pitStop => {
      const competitor = competitorMap.get(pitStop.competitorId);
      const team = competitor ? teamMap.get(competitor.teamId) : null;
      return {
        ...pitStop.toObject(),
        competitorName: competitor?.name || `Competitor ${pitStop.competitorId}`,
        teamName: team?.name || `Team ${pitStop.teamId}`,
        kartNumber: pitStop.kartNumber
      };
    });

    // Create team statistics with relationships
    const teamStats = await Promise.all(teams.map(async (team) => {
      const teamLaps = await ApexLap.countDocuments({ sessionId, teamId: team.teamId });
      const teamPitStops = await ApexPitStop.countDocuments({ sessionId, teamId: team.teamId });
      const fastestLap = await ApexLap.findOne({ sessionId, teamId: team.teamId }).sort({ lapTime: 1 });

      return {
        ...team.toObject(),
        statistics: {
          totalLaps: teamLaps,
          pitStops: teamPitStops,
          fastestLap: fastestLap ? {
            lapTime: fastestLap.lapTime,
            lapTimeFormatted: fastestLap.lapTimeFormatted,
            competitorId: fastestLap.competitorId
          } : null
        }
      };
    }));

    res.json({
      session,
      teams: teamStats,
      karts,
      competitors,
      recentLaps: enhancedLaps,
      recentPitStops: enhancedPitStops,
      counts: {
        teams: teams.length,
        karts: karts.length,
        competitors: competitors.length,
        totalLaps: await ApexLap.countDocuments({ sessionId: sessionObjectId }),
        totalPitStops: await ApexPitStop.countDocuments({ sessionId: sessionObjectId })
      },
      relationships: {
        teamsToKarts: teams.map(team => ({
          teamId: team.teamId,
          teamName: team.name,
          kartNumber: team.kartNumber,
          kartExists: karts.some(kart => kart.kartNumber === team.kartNumber)
        })),
        competitorsToTeams: competitors.map(comp => ({
          competitorId: comp.competitorId,
          teamId: comp.teamId,
          teamExists: teams.some(team => team.teamId === comp.teamId)
        }))
      }
    });
  } catch (error) {
    console.error('Error getting session details:', error);
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Failed to get session details'
    });
  }
});

// Get recent laps with details
router.get('/laps/recent', async (req: Request, res: Response) => {
  try {
    const limit = parseInt(req.query.limit as string) || 20;
    const sessionIdentifier = req.query.sessionId as string;

    let query: any = {};

    if (sessionIdentifier) {
      // Find session by ObjectId or title
      const session = await findSessionByIdOrTitle(sessionIdentifier);
      if (session) {
        query.sessionId = session._id;
      } else {
        res.status(404).json({ error: 'Session not found' });
        return;
      }
    }

    const laps = await ApexLap.find(query)
      .sort({ timestamp: -1 })
      .limit(limit)
      .lean();

    // Add competitor and team names by looking them up separately
    const lapsWithDetails = await Promise.all(
      laps.map(async (lap) => {
        const [competitor, team] = await Promise.all([
          ApexCompetitor.findOne({
            sessionId: lap.sessionId,
            competitorId: lap.competitorId
          }),
          ApexTeam.findOne({
            sessionId: lap.sessionId,
            teamId: lap.teamId
          })
        ]);

        return {
          ...lap,
          competitorName: competitor?.name || `Competitor ${lap.competitorId}`,
          teamName: team?.name || `Team ${lap.teamId}`,
          kartNumber: lap.kartNumber
        };
      })
    );

    res.json(lapsWithDetails);
  } catch (error) {
    console.error('Error getting recent laps:', error);
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Failed to get recent laps'
    });
  }
});

// Get lap times for a specific competitor
router.get('/competitors/:competitorId/laps', async (req: Request, res: Response) => {
  try {
    const { competitorId } = req.params;
    const sessionIdentifier = req.query.sessionId as string;
    const limit = parseInt(req.query.limit as string) || 50;

    const query: any = { competitorId };
    let sessionObjectId: mongoose.Types.ObjectId | undefined;

    if (sessionIdentifier) {
      // Find session by ObjectId or title
      const session = await findSessionByIdOrTitle(sessionIdentifier);
      if (session) {
        sessionObjectId = session._id;
        query.sessionId = sessionObjectId;
      } else {
        res.status(404).json({ error: 'Session not found' });
        return;
      }
    }

    const laps = await ApexLap.find(query)
      .sort({ timestamp: -1 })
      .limit(limit);

    const [competitor, team] = await Promise.all([
      sessionObjectId ? ApexCompetitor.findOne({ competitorId, sessionId: sessionObjectId }) : null,
      sessionObjectId ? ApexTeam.findOne({ sessionId: sessionObjectId, teamId: competitorId }) : null
    ]);

    res.json({
      competitor: competitor || { competitorId, name: `Competitor ${competitorId}` },
      team: team || null,
      laps,
      stats: {
        totalLaps: laps.length,
        bestLap: laps.length > 0 ? laps.reduce((best, lap) =>
          !best || lap.lapTime < best.lapTime ? lap : best) : null,
        averageLapTime: laps.length > 0 ?
          laps.reduce((sum, lap) => sum + lap.lapTime, 0) / laps.length : 0
      }
    });
  } catch (error) {
    console.error('Error getting competitor laps:', error);
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Failed to get competitor laps'
    });
  }
});

// Clear all apex data (for testing)
router.delete('/clear-all', async (req: Request, res: Response) => {
  try {
    const results = await Promise.all([
      ApexSession.deleteMany({}),
      ApexTeam.deleteMany({}),
      ApexKart.deleteMany({}),
      ApexCompetitor.deleteMany({}),
      ApexLap.deleteMany({}),
      ApexPitStop.deleteMany({}),
      ApexLiveData.deleteMany({})
    ]);

    const totalDeleted = results.reduce((sum, result) => sum + result.deletedCount, 0);

    res.json({
      success: true,
      message: `Cleared all apex data. Deleted ${totalDeleted} documents.`,
      details: {
        sessions: results[0].deletedCount,
        teams: results[1].deletedCount,
        karts: results[2].deletedCount,
        competitors: results[3].deletedCount,
        laps: results[4].deletedCount,
        pitStops: results[5].deletedCount,
        liveData: results[6].deletedCount
      }
    });
  } catch (error) {
    console.error('Error clearing apex data:', error);
    res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to clear apex data' 
    });
  }
});

// Get real-time parsing status
router.get('/parsing-status', async (req: Request, res: Response) => {
  try {
    // Get the most recent data to show parsing activity
    const [recentSession, recentLap, recentTeam] = await Promise.all([
      ApexSession.findOne().sort({ createdAt: -1 }),
      ApexLap.findOne().sort({ timestamp: -1 }),
      ApexTeam.findOne().sort({ createdAt: -1 })
    ]);

    const now = new Date();
    const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);

    const recentActivity = {
      sessionsCreated: await ApexSession.countDocuments({ 
        createdAt: { $gte: fiveMinutesAgo } 
      }),
      lapsRecorded: await ApexLap.countDocuments({ 
        timestamp: { $gte: fiveMinutesAgo } 
      }),
      teamsCreated: await ApexTeam.countDocuments({ 
        createdAt: { $gte: fiveMinutesAgo } 
      })
    };

    res.json({
      isActive: recentActivity.lapsRecorded > 0 || recentActivity.sessionsCreated > 0,
      recentActivity,
      lastActivity: {
        session: recentSession ? {
          sessionId: recentSession._id,
          title: `${recentSession.title1} - ${recentSession.title2}`,
          createdAt: recentSession.createdAt
        } : null,
        lap: recentLap ? {
          competitorId: recentLap.competitorId,
          lapTime: recentLap.lapTimeFormatted,
          timestamp: recentLap.timestamp
        } : null,
        team: recentTeam ? {
          name: recentTeam.name,
          kartNumber: recentTeam.kartNumber,
          createdAt: recentTeam.createdAt
        } : null
      }
    });
  } catch (error) {
    console.error('Error getting parsing status:', error);
    res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to get parsing status' 
    });
  }
});

// Test database connection
router.get('/health', async (req: Request, res: Response) => {
  try {
    // Simple database query to test connection
    const sessionCount = await ApexSession.countDocuments();
    
    res.json({
      status: 'healthy',
      database: 'connected',
      timestamp: new Date().toISOString(),
      totalSessions: sessionCount
    });
  } catch (error) {
    console.error('Database health check failed:', error);
    res.status(500).json({
      status: 'unhealthy',
      database: 'disconnected',
      error: error instanceof Error ? error.message : 'Database connection failed',
      timestamp: new Date().toISOString()
    });
  }
});

export default router;
