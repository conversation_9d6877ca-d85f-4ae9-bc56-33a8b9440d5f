import { EventEmitter } from 'events';
import { parseLogMessage, ParsedMessage } from './apexMessageParser';

export interface ReplayConfig {
  speed: number; // Replay speed multiplier (1.0 = normal, 2.0 = 2x speed, etc.)
  autoStart: boolean;
  loopMode: boolean;
  skipTimestamps: boolean;
  realTimeMode: boolean; // Use real-time intervals between messages
}

export interface ReplayStatus {
  isPlaying: boolean;
  isPaused: boolean;
  currentLine: number;
  totalLines: number;
  progress: number;
  elapsedTime: number;
  estimatedTimeRemaining: number;
}

interface MessageWithTimestamp {
  content: string;
  timestamp: Date | null;
  originalLine: string;
}

export class LogReplayService extends EventEmitter {
  private logMessages: MessageWithTimestamp[] = [];
  private currentIndex: number = 0;
  private isPlaying: boolean = false;
  private isPaused: boolean = false;
  private replayTimer: NodeJS.Timeout | null = null;
  private startTime: number = 0;
  private replayStartTime: Date | null = null;
  private config: ReplayConfig;
  private baseInterval: number = 100; // Base interval in milliseconds

  constructor(config: Partial<ReplayConfig> = {}) {
    super();
    this.config = {
      speed: 1.0,
      autoStart: false,
      loopMode: false,
      skipTimestamps: true,
      realTimeMode: true,
      ...config
    };
  }

  /**
   * Load log content for replay
   */
  loadLog(logContent: string): void {
    this.stop();

    // Parse log into messages with timestamps
    this.logMessages = this.parseLogIntoMessages(logContent);
    this.currentIndex = 0;

    console.log(`Loaded log with ${this.logMessages.length} messages`);
    this.emit('logLoaded', {
      totalLines: this.logMessages.length,
      firstLine: this.logMessages[0]?.content || '',
      lastLine: this.logMessages[this.logMessages.length - 1]?.content || ''
    });
  }

  /**
   * Parse log content into complete messages with timestamps
   * For Apex logs, messages are separated by empty lines or timestamps
   */
  private parseLogIntoMessages(logContent: string): MessageWithTimestamp[] {
    // Split by lines but preserve empty lines for message separation
    const allLines = logContent.split('\n');
    const messages: MessageWithTimestamp[] = [];
    let currentMessage: string[] = [];
    let currentTimestamp: Date | null = null;

    for (let i = 0; i < allLines.length; i++) {
      const line = allLines[i]?.trim() || '';

      // Empty line or timestamp indicates message boundary
      if (line === '' || line.startsWith('[')) {
        // If we have accumulated lines, save them as a message
        if (currentMessage.length > 0) {
          const messageContent = currentMessage.join('\n');
          const originalLine = currentMessage.join('\n');

          messages.push({
            content: messageContent,
            timestamp: currentTimestamp,
            originalLine
          });
          currentMessage = [];
        }

        // If this is a timestamp line, extract timestamp and start new message
        if (line.startsWith('[')) {
          currentTimestamp = this.extractTimestamp(line);
          currentMessage.push(line);
        }
      } else {
        // Add non-empty line to current message
        currentMessage.push(line);
      }
    }

    // Don't forget the last message
    if (currentMessage.length > 0) {
      const messageContent = currentMessage.join('\n');
      const originalLine = currentMessage.join('\n');

      messages.push({
        content: messageContent,
        timestamp: currentTimestamp,
        originalLine
      });
    }

    // Filter out empty messages
    const filteredMessages = messages.filter(msg => msg.content.trim().length > 0);

    console.log(`Parsed ${allLines.length} lines into ${filteredMessages.length} complete message blocks`);
    return filteredMessages;
  }

  /**
   * Extract timestamp from a log line
   */
  private extractTimestamp(line: string): Date | null {
    const timestampMatch = line.match(/^\[([^\]]+)\]/);
    if (timestampMatch && timestampMatch[1]) {
      try {
        return new Date(timestampMatch[1]);
      } catch (error) {
        console.warn('Failed to parse timestamp:', timestampMatch[1]);
        return null;
      }
    }
    return null;
  }



  /**
   * Start replay
   */
  start(): void {
    if (this.logMessages.length === 0) {
      throw new Error('No log loaded. Call loadLog() first.');
    }

    if (this.isPlaying) {
      console.log('Replay already playing');
      return;
    }

    this.isPlaying = true;
    this.isPaused = false;
    this.startTime = Date.now();
    this.replayStartTime = this.logMessages[0]?.timestamp || new Date();

    console.log(`Starting replay at speed ${this.config.speed}x`);
    this.emit('replayStarted', this.getStatus());

    this.scheduleNextMessage();
  }

  /**
   * Pause replay
   */
  pause(): void {
    if (!this.isPlaying || this.isPaused) {
      return;
    }

    this.isPaused = true;
    if (this.replayTimer) {
      clearTimeout(this.replayTimer);
      this.replayTimer = null;
    }

    console.log('Replay paused');
    this.emit('replayPaused', this.getStatus());
  }

  /**
   * Resume replay
   */
  resume(): void {
    if (!this.isPlaying || !this.isPaused) {
      return;
    }

    this.isPaused = false;
    console.log('Replay resumed');
    this.emit('replayResumed', this.getStatus());
    
    this.scheduleNextMessage();
  }

  /**
   * Stop replay
   */
  stop(): void {
    this.isPlaying = false;
    this.isPaused = false;
    
    if (this.replayTimer) {
      clearTimeout(this.replayTimer);
      this.replayTimer = null;
    }

    console.log('Replay stopped');
    this.emit('replayStopped', this.getStatus());
  }

  /**
   * Reset replay to beginning
   */
  reset(): void {
    this.stop();
    this.currentIndex = 0;
    console.log('Replay reset to beginning');
    this.emit('replayReset', this.getStatus());
  }

  /**
   * Seek to specific line
   */
  seekToLine(lineNumber: number): void {
    if (lineNumber < 0 || lineNumber >= this.logMessages.length) {
      throw new Error(`Invalid line number: ${lineNumber}`);
    }

    const wasPlaying = this.isPlaying;
    this.stop();

    this.currentIndex = lineNumber;
    console.log(`Seeked to line ${lineNumber}`);
    this.emit('replaySeek', this.getStatus());

    if (wasPlaying && this.config.autoStart) {
      this.start();
    }
  }

  /**
   * Set replay speed
   */
  setSpeed(speed: number): void {
    if (speed <= 0) {
      throw new Error('Speed must be greater than 0');
    }

    this.config.speed = speed;
    console.log(`Replay speed set to ${speed}x`);
    this.emit('speedChanged', { speed });
  }

  /**
   * Get current replay status
   */
  getStatus(): ReplayStatus {
    const progress = this.logMessages.length > 0 ? this.currentIndex / this.logMessages.length : 0;
    const elapsedTime = this.startTime > 0 ? Date.now() - this.startTime : 0;
    const estimatedTotal = elapsedTime > 0 ? elapsedTime / progress : 0;
    const estimatedTimeRemaining = estimatedTotal - elapsedTime;

    return {
      isPlaying: this.isPlaying,
      isPaused: this.isPaused,
      currentLine: this.currentIndex,
      totalLines: this.logMessages.length,
      progress,
      elapsedTime,
      estimatedTimeRemaining: Math.max(0, estimatedTimeRemaining)
    };
  }

  /**
   * Schedule next message to be sent
   */
  private scheduleNextMessage(): void {
    if (!this.isPlaying || this.isPaused) {
      return;
    }

    if (this.currentIndex >= this.logMessages.length) {
      // End of log reached
      if (this.config.loopMode) {
        this.currentIndex = 0;
        console.log('Replay looping back to beginning');
        this.emit('replayLoop', this.getStatus());
      } else {
        console.log('Replay completed');
        this.emit('replayCompleted', this.getStatus());
        this.stop();
        return;
      }
    }

    const currentMessage = this.logMessages[this.currentIndex];
    if (!currentMessage) {
      console.warn('No message found at index:', this.currentIndex);
      return;
    }

    // Calculate interval based on real-time mode or base interval
    let interval = this.baseInterval / this.config.speed;

    if (this.config.realTimeMode && this.currentIndex > 0) {
      const prevMessage = this.logMessages[this.currentIndex - 1];
      if (currentMessage.timestamp && prevMessage?.timestamp) {
        const realInterval = currentMessage.timestamp.getTime() - prevMessage.timestamp.getTime();
        interval = Math.max(10, realInterval / this.config.speed); // Minimum 10ms interval
      }
    }

    this.replayTimer = setTimeout(() => {
      this.sendMessage(currentMessage);
      this.currentIndex++;
      this.emit('replayProgress', this.getStatus());
      this.scheduleNextMessage();
    }, interval);
  }

  /**
   * Send message via websocket
   */
  private sendMessage(messageWithTimestamp: MessageWithTimestamp): void {
    try {
      const { content, timestamp, originalLine } = messageWithTimestamp;

      // Remove timestamp if configured to do so
      let messageContent = content;
      if (this.config.skipTimestamps) {
        // Remove timestamp pattern [YYYY-MM-DDTHH:mm:ss.sssZ]
        messageContent = content.replace(/^\[([^\]]+)\]\s*/, '');
      }

      // Parse the message to validate format
      const parsedMessage = parseLogMessage(content);
      if (!parsedMessage) {
        console.warn('Failed to parse message:', content);
        return;
      }

      // Emit the message with both original and parsed versions
      this.emit('message', {
        originalMessage: originalLine,
        parsedMessage: parsedMessage,
        content: messageContent,
        timestamp: timestamp,
        lineNumber: this.currentIndex
      });

      console.log(`Sent message ${this.currentIndex}: ${messageContent.substring(0, 100)}...`);

    } catch (error) {
      console.error('Error sending message:', error);
      this.emit('error', { error, message: messageWithTimestamp, lineNumber: this.currentIndex });
    }
  }

  /**
   * Get configuration
   */
  getConfig(): ReplayConfig {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<ReplayConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('Replay configuration updated:', this.config);
    this.emit('configUpdated', this.config);
  }

  /**
   * Get current message content
   */
  getCurrentLine(): string | null {
    if (this.currentIndex >= 0 && this.currentIndex < this.logMessages.length) {
      return this.logMessages[this.currentIndex]?.content || null;
    }
    return null;
  }

  /**
   * Get message by index
   */
  getLine(index: number): string | null {
    if (index >= 0 && index < this.logMessages.length) {
      return this.logMessages[index]?.content || null;
    }
    return null;
  }

  /**
   * Get total number of messages
   */
  getTotalLines(): number {
    return this.logMessages.length;
  }
}

export default LogReplayService;
